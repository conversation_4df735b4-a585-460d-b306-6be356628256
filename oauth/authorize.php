<?php
if ($_SERVER['REQUEST_METHOD'] === 'GET' && strpos($_SERVER['REQUEST_URI'], '/oauth/authorize') !== false) {
    $client_id = $_GET['client_id'] ?? '';
    $redirect_uri = $_GET['redirect_uri'] ?? '';
    $state = $_GET['state'] ?? '';

    // Exibe o formulário de login
    echo '<form method="POST" action="/oauth/authorize">';
    echo '<input type="hidden" name="client_id" value="'.$client_id.'">';
    echo '<input type="hidden" name="redirect_uri" value="'.$redirect_uri.'">';
    echo '<input type="hidden" name="state" value="'.$state.'">';
    echo 'Usuário: <input name="usuario"><br>';
    echo 'Senha: <input type="password" name="senha"><br>';
    echo '<input type="submit" value="Entrar">';
    echo '</form>';
    exit;
}


if ($_SERVER['REQUEST_METHOD'] === 'POST' && strpos($_SERVER['REQUEST_URI'], '/oauth/authorize') !== false) {
    $usuario = $_POST['usuario'];
    $senha = $_POST['senha'];
    $redirect_uri = $_POST['redirect_uri'];
    $state = $_POST['state'];

    $wsTotvs = new WSTotvs();
    if ($wsTotvs->login($usuario, $senha)) {
        // Gera um código temporário simples (em produção, use algo mais seguro)
        $code = bin2hex(random_bytes(16));
        file_put_contents("/tmp/oauth_codes/{$code}", json_encode([
            'username' => $usuario
        ]));

        $url = $redirect_uri . '?code=' . $code . '&state=' . urlencode($state);
        header("Location: $url");
        exit;
    } else {
        echo "Login falhou";
        exit;
    }
}
