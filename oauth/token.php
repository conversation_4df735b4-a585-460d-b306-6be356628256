<?php 

if ($_SERVER['REQUEST_METHOD'] === 'POST' && strpos($_SERVER['REQUEST_URI'], '/oauth/token') !== false) {
    $code = $_POST['code'] ?? '';

    $file = "/tmp/oauth_codes/{$code}";
    if (!file_exists($file)) {
        http_response_code(400);
        echo json_encode(['error' => 'invalid_grant']);
        exit;
    }

    $data = json_decode(file_get_contents($file), true);
    $token = bin2hex(random_bytes(32));
    file_put_contents("/tmp/oauth_tokens/{$token}", json_encode([
        'username' => $data['username']
    ]));

    unlink($file); // Consome o código

    header('Content-Type: application/json');
    echo json_encode([
        'access_token' => $token,
        'token_type' => 'Bearer',
        'expires_in' => 3600
    ]);
    exit;
}
