<?php
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);
class WSTotvs {
    private $_basicParams;
    private $_optionSoapLogin;
    private $_locationLogin;

    // private $_config = array(
    //     'hostTbc' => 'https://faculdadetrevisan150170.rm.cloudtotvs.com.br:8051/',
    //     'user' => 'edvaldoribeiro.pvt',
    //     'pass' => 'Picanha@2023',
    //     'tbcType' => 'host',
    // );
    // homologACAO
    private $_config = array(
        'hostTbc' => 'https://faculdadetrevisan150171.rm.cloudtotvs.com.br:2106/', 
        'user'    => 'edvaldoribeiro.pvt', 
        'pass'    => 'Picanha@2023', 
    );

    public function __construct() {
        $this->_basicParams = array(
            'trace'       => true,
            'login'       => $this->_config["user"],
            'password'    => $this->_config["pass"],
            'exceptions'  => true,
            'cache_wsdl'  => WSDL_CACHE_NONE
        );

        $this->_locationLogin = $this->_config["hostTbc"] . 'wsDataServer/IwsBase';
        ini_set('soap.wsdl_cache_enabled', 0);
        ini_set('soap.wsdl_cache_ttl', 0);

        $this->_optionSoapLogin = array('location' => $this->_locationLogin);

        try {
            new \SoapClient($this->_config["hostTbc"] . 'wsDataServer/MEX?WSDL', $this->_basicParams);
        } catch (\SoapFault $e) {
            error_log("Erro SOAP no WSTotvs: " . $e->getMessage());
            throw new \Exception("Erro ao carregar WSDL do TOTVS: " . $e->getMessage(), 0, $e);
        }
    }

    // public function getEmployeeId($codUsuario){
    //     $function = "RealizarConsultaSQL";
    //     $xmlRecord = array(
    //         "RealizarConsultaSQL" => array(
    //             "codSentenca" => "DEGREED.001",
    //             "codColigada" => "0",
    //             "codSistema" => "S",
    //             "parameters" => "CODUSUARIO=".$codUsuario
    //         )
    //     );
    //     try {
    //         $result = $this->_clientSoapWsConsultaSql->__soapCall($function, $xmlRecord,   array('location' => $this->_config["hostTbc"] . "/wsConsultaSQL/IwsConsultaSQL")) ;
    //         var_dump( $result);
    //         exit;
    //         //$xml = new \SimpleXMLElement($result->RealizarConsultaSQLResult);
    //         $xml = simplexml_load_string($result->RealizarConsultaSQLResult);
    //         $employeeid = (array)$xml->Resultado->employeeid;
    //         return $employeeid[0];
    //     } catch (\Exception $e) {
    //         return $e;           
    //     }
    // }

    public function login($usuario, $senha): bool {
        $function = "AutenticaAcesso";
        $xmlRecord = ["AutenticaAcesso" => []];

        $loginParams = array(
            'trace'       => true,
            'login'       => $usuario,
            'password'    => $senha,
            'exceptions'  => true,
            'cache_wsdl'  => WSDL_CACHE_NONE
        );

        $wsdlUrl = $this->_config["hostTbc"] . 'wsDataServer/MEX?WSDL';

        try {
            $clientLoginSoap = new \SoapClient($wsdlUrl, $loginParams);
            $clientLoginSoap->__soapCall($function, $xmlRecord, $this->_optionSoapLogin);
            return true;
        } catch (\SoapFault $sf) {
            error_log("Erro de login RM: " . $sf->getMessage());
            return false;
        } catch (\Exception $e) {
            error_log("Erro geral no login RM: " . $e->getMessage());
            throw $e;
        }
    }
}
