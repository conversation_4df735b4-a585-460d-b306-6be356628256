<?php 

// header("Content-type: text/html; charset=utf-8");

$config = array(
    'wstotvs' => array(
        'hostTbc' => 'https://faculdadetrevisan150171.rm.cloudtotvs.com.br:2106/',
        'user' => 'edvaldoribeiro.pvt',
        'pass' => 'Picanha@2023',
        'tbcType' => 'host',
        'urlsRequisicaoPermitidas' => array('http://localhost/','https://estudante.trevisan.edu.br/', 'https://homologa.estudante.trevisan.edu.br:2104/', 'https://homologa.estudante.trevisan.edu.br/')
    ),
    'wstotvsrest' => array(
        'host' => 'https://faculdadetrevisan150171.rm.cloudtotvs.com.br',
        'port' => '2106',
        'user' => 'edvaldoribeiro.pvt',  
        'pass' => 'Picanha@2023'
    )
);
// $config = array(
//     'wstotvs' => array(
//         'hostTbc' => 'https://faculdadetrevisan150170.rm.cloudtotvs.com.br:8051/',
//         'user' => 'edvaldoribeiro.pvt',
//         'pass' => 'Picanha@2023',
//         'tbcType' => 'host',
//         'urlsRequisicaoPermitidas' => array('http://localhost/','https://estudante.trevisan.edu.br/', 'https://homologa.estudante.trevisan.edu.br:2104/', 'https://homologa.estudante.trevisan.edu.br/')
//     ),
//     'wstotvsrest' => array(
//         'host' => 'https://faculdadetrevisan150170.rm.cloudtotvs.com.br',
//         'port' => '8051',
//         'user' => 'edvaldoribeiro.pvt',  
//         'pass' => 'Picanha@2023'
//     )
    

// );

?>
