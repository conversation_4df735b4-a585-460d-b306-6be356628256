<?php
// oauth-server.php
error_reporting(E_ALL);
ini_set('display_errors', 1);

require_once "WSTotvs.php"; 

$codeDir = '/tmp/oauth_codes';
$tokenDir = '/tmp/oauth_tokens';
if (!is_dir($codeDir)) mkdir($codeDir, 0777, true);
if (!is_dir($tokenDir)) mkdir($tokenDir, 0777, true);

//  (mock)
$fakeDb = [
    'clients' => [
        'moodle' => [
            'client_secret' => 'segredo123',
            'redirect_uri' => 'https://faculdadetrevisan-sandbox.mrooms.net/admin/oauth2callback.php',
        ]
    ],
];


function generateRandomString($length = 40) {
    return bin2hex(random_bytes($length / 2));
}


$action = $_GET['action'] ?? null; 

if (!$action && isset($_SERVER['PATH_INFO'])) {
    $action = ltrim($_SERVER['PATH_INFO'], '/');
}


if ($action === 'authorize') {
    $client_id = $_GET['client_id'] ?? null;
    $redirect_uri = $_GET['redirect_uri'] ?? null;
    $state = $_GET['state'] ?? null;

    if (!isset($fakeDb['clients'][$client_id]) || $fakeDb['clients'][$client_id]['redirect_uri'] !== $redirect_uri) {
        http_response_code(400);
        echo "Client ID ou Redirect URI inválido.";
        exit;
    }

    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        $username = $_POST['username'] ?? '';
        $password = $_POST['password'] ?? '';

        $ws = new WSTotvs();
        if ($ws->login($username, $password)) {
            $code = generateRandomString(30);
            file_put_contents("$codeDir/$code.json", json_encode([
                'username' => $username,
                'expires' => time() + 300
            ]));

            $redirect = $redirect_uri . "?code=" . urlencode($code);
            if ($state) {
                $redirect .= "&state=" . urlencode($state);
            }
            header("Location: $redirect");
            exit;
        } else {
            echo "Usuário ou senha inválidos.";
        }
    }

    echo <<<HTML
    <h1>Login OAuth2</h1>
    <form method="post">
        <input type="text" name="username" placeholder="Usuário" required><br>
        <input type="password" name="password" placeholder="Senha" required><br>
        <button type="submit">Entrar</button>
    </form>
    HTML;
    exit;
}

// === /token ===
if ($action === 'token') {
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        http_response_code(405);
        echo "Método não permitido.";
        exit;
    }

    $client_id = $_POST['client_id'] ?? '';
    $client_secret = $_POST['client_secret'] ?? '';
    $code = $_POST['code'] ?? '';

    $client = $fakeDb['clients'][$client_id] ?? null;
    if (!$client || $client['client_secret'] !== $client_secret) {
        http_response_code(400);
        echo json_encode(['error' => 'invalid_client']);
        exit;
    }

    $codeFile = "$codeDir/$code.json";
    if (!file_exists($codeFile)) {
        http_response_code(400);
        echo json_encode(['error' => 'invalid_grant']);
        exit;
    }

    $data = json_decode(file_get_contents($codeFile), true);
    unlink($codeFile);

    if ($data['expires'] < time()) {
        http_response_code(400);
        echo json_encode(['error' => 'expired_code']);
        exit;
    }

    $username = $data['username'];
    $token = generateRandomString(40);
    file_put_contents("$tokenDir/$token.json", json_encode([
        'username' => $username,
        'expires' => time() + 3600
    ]));

    echo json_encode([
        'access_token' => $token,
        'token_type' => 'Bearer',
        'expires_in' => 3600,
    ]);
    exit;
}

if ($action === 'userinfo') {
    $auth = $_SERVER['HTTP_AUTHORIZATION'] ?? '';
    if (!preg_match('/Bearer\s(\S+)/', $auth, $matches)) {
        http_response_code(401);
        echo json_encode(['error' => 'unauthorized']);
        exit;
    }

    $token = $matches[1];
    $tokenFile = "$tokenDir/$token.json";
    if (!file_exists($tokenFile)) {
        http_response_code(401);
        echo json_encode(['error' => 'invalid_token']);
        exit;
    }

    $data = json_decode(file_get_contents($tokenFile), true);
    if ($data['expires'] < time()) {
        unlink($tokenFile);
        http_response_code(401);
        echo json_encode(['error' => 'expired_token']);
        exit;
    }

    $username = $data['username'];
    echo json_encode([
        'sub' => $username,
        'email' => $username . '@trevisan.edu.br',
        'name' => $username,
        'preferred_username' => $username,
    ]);
    exit;
}

http_response_code(404);
echo "Rota não encontrada.";
exit;