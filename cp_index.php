<?php
 
 ini_set('display_errors', 1);
 ini_set('display_startup_errors', 1);
 error_reporting(E_ALL);
 
$config = [
    'wstotvs' => [
        'hostTbc' => 'https://faculdadetrevisan150170.rm.cloudtotvs.com.br:8051/', // Exemplo de WSTotvs.txt
        'user' => 'edvaldoribeiro.pvt', // Usuário de SISTEMA
        'pass' => 'Picanha@2023',      // Senha de SISTEMA
    ],
    'consulta_usuario' => [
        'codSentenca' => 'DEGREED.001', // IMPORTANTE: Use a sentença SQL correta do seu RM que busca usuário por login/código
        'codColigada' => '0',           // Código da Coligada (ajuste se necessário)
        'codSistema' => 'S',            // Código do Sistema (ajuste se necessário)
        'paramName' => 'CODUSUARIO',    // IMPORTANTE: Nome do parâmetro esperado pela sentença SQL (ex: 'CODUSUARIO', 'LOGIN', 'CPF')
    ]
];

function check_user_exists_rm(string $userIdentifier, array $wsConfig, array $queryConfig) {
    $wsdlUrl = rtrim($wsConfig['hostTbc'], '/') . '/wsConsultaSQL/MEX?WSDL'; // WSDL do WsConsultaSQL
    $locationUrl = rtrim($wsConfig['hostTbc'], '/') . '/wsConsultaSQL/IwsConsultaSQL'; // Location do WsConsultaSQL

    // Parâmetros básicos para a conexão SOAP (autenticação do SISTEMA)
    $soapOptions = [
        'trace' => true,
        'exceptions' => true,
        'login' => $wsConfig['user'], // Credenciais do SISTEMA
        'password' => $wsConfig['pass'], // Credenciais do SISTEMA
        'cache_wsdl' => WSDL_CACHE_NONE,
         // Adicionar contexto SSL se necessário (como em WSTotvs.class.txt)
         'stream_context' => stream_context_create([
             'ssl' => [
                 'verify_peer' => false, // ATENÇÃO: Não use em produção sem entender os riscos
                 'verify_peer_name' => false,
             ],
         ]),
    ];

    // Parâmetros para o método RealizarConsultaSQL
    $queryParams = [
        'RealizarConsultaSQL' => [
            'codSentenca' => $queryConfig['codSentenca'],
            'codColigada' => $queryConfig['codColigada'],
            'codSistema' => $queryConfig['codSistema'],
            'parameters' => $queryConfig['paramName'] . '=' . $userIdentifier
        ]
    ];

    $callOptions = ['location' => $locationUrl]; //

        try {
            $soapClient = new SoapClient($wsdlUrl, $soapOptions);
           return $result = $soapClient->__soapCall('RealizarConsultaSQL', $queryParams, $callOptions);
            print_r($result);
        } catch (SoapFault $e) {
            echo "Erro ao autenticar ou executar a consulta:\n";
            echo $e->getMessage(); // Aqui vem a mensagem como "Usuário ou senha inválido"
        }
 
}


  function login($usuario, $senha, $config){

    $function = "AutenticaAcesso";
    $xmlRecord = array(
        "AutenticaAcesso" => array()
    );

    $loginParams = array(
        'trace'=> TRUE,
        'login' => $usuario,
        'password' => $senha,    
        'exceptions'=>true,     
        'cache_wsdl'=> WSDL_CACHE_NONE 
    );

    $clientSoap = new \SoapClient($config["hostTbc"] . 'wsDataServer/MEX?WSDL', $loginParams);

    $optionSoap = array('location' => 'https://faculdadetrevisan150170.rm.cloudtotvs.com.br:8051/wsDataServer/IwsBase');

  return  $result = $clientSoap->__soapCall($function, $xmlRecord, $optionSoap);
print_R($result );
    return isset($result->AutenticaAcessoResult) ? $result->AutenticaAcessoResult : simplexml_load_string($result);
}
 
 /**
  * Tenta autenticar o usuário no Totvs RM via SOAP.
  * @param string $username Usuário final
  * @param string $password Senha do usuário final
  * @param array $rmConfig Configuração do WS RM
  * @return bool|string True em sucesso, mensagem de erro em falha.
  */
 function authenticate_rm(string $username, string $password, array $rmConfig) {
     $wsdlUrl = rtrim($rmConfig['hostTbc'], '/') . '/wsDataServer/MEX?WSDL'; // [cite: 148, 154]
     $locationUrl = rtrim($rmConfig['hostTbc'], '/') . '/wsDataServer/IwsBase'; // [cite: 148, 150]
 
     // Parâmetros básicos para a conexão SOAP (autenticação do SISTEMA) [cite: 147]
     $soapOptions = [
         'trace' => true, // Habilitar trace para debug
         'exceptions' => true, // Lançar exceções em erros SOAP
         'login' => $rmConfig['user'],
         'password' => $rmConfig['pass'],
         'cache_wsdl' => WSDL_CACHE_NONE, // Desabilitar cache WSDL para dev/teste [cite: 23, 81]
          // Adicionar contexto SSL se necessário (como em WSTotvs.class.txt [cite: 23, 24])
          'stream_context' => stream_context_create([
              'ssl' => [
                  'verify_peer' => false, // ATENÇÃO: Não use em produção sem entender os riscos
                  'verify_peer_name' => false,
              ],
          ]),
     ];
 
      // Parâmetros para o método AutenticaAcesso (credenciais do USUÁRIO FINAL) [cite: 152, 153]
      $authParams = [
         'AutenticaAcesso' => [] // O método pode não precisar de parâmetros explícitos aqui se a autenticação é via HTTP Basic na conexão
      ];
      // OU, se o método esperar usuário/senha como parâmetros:
      // $authParams = [
      //    'AutenticaAcesso' => [
      //        'usuario' => $username,
      //        'senha' => $password
      //    ]
      // ];
      // É CRUCIAL verificar a definição exata do WSDL para AutenticaAcesso
 
      // Opções específicas da chamada SOAP (Location) [cite: 150, 155]
      $callOptions = ['location' => $locationUrl];
 
    //  try {
         //write_log("Tentando autenticação RM para usuário: {$username}");
 
         // --- Tentativa 1: Autenticação na CONEXÃO SOAP ---
         // Alguns WSDLs podem autenticar apenas na conexão inicial.
         // Se AutenticaAcesso não receber usuário/senha, a autenticação pode ocorrer aqui.
         // Precisamos criar um SoapClient COM as credenciais do usuário FINAL para testar isso.
         $userSoapOptions = $soapOptions;
         $userSoapOptions['login'] = $username;
         $userSoapOptions['password'] = $password;
 
         $soapClientUserAuth = new SoapClient($wsdlUrl, $userSoapOptions);
      return   $resultUserAuth = $soapClientUserAuth->__soapCall('AutenticaAcesso', $authParams, $callOptions); // [cite: 155]
            print_r($resultUserAuth );
            exit;
        //  // Verifica o resultado - AJUSTE CONFORME A RESPOSTA REAL DO SEU WEBSERVICE [cite: 156]
        //  if (isset($resultUserAuth->AutenticaAcessoResult) && $resultUserAuth->AutenticaAcessoResult == 1) {
        //       write_log("Sucesso na autenticação RM (via conexão) para usuário: {$username}");
        //       return true;
        //  }
 
         // --- Tentativa 2: Autenticação via MÉTODO SOAP ---
         // Se a Tentativa 1 não for o caso ou falhar, tentamos com as credenciais de SISTEMA
         // e esperamos que o AutenticaAcesso valide o usuário/senha passados (se aplicável).
         // Nota: WSTotvs.txt [cite: 154] cria um NOVO SoapClient aqui com credenciais do usuário.
         // Se AutenticaAcesso NÃO recebe user/pass, a lógica de WSTotvs.txt é a correta.
         // Se AutenticaAcesso RECEBE user/pass, então se usa o client com credenciais de sistema.
 
         // Recriando cliente com credenciais do usuário final (como em WSTotvs.txt [cite: 154])
          $soapClientMethodAuth = new SoapClient($wsdlUrl, $userSoapOptions); // Usando credenciais do usuário final
          $resultMethodAuth = $soapClientMethodAuth->__soapCall('AutenticaAcesso', $authParams, $callOptions); // [cite: 155]
          print_r($resultMethodAuth );
          exit;
         // Verifica o resultado - AJUSTE CONFORME A RESPOSTA REAL DO SEU WEBSERVICE [cite: 156]
         if (isset($resultMethodAuth->AutenticaAcessoResult) && $resultMethodAuth->AutenticaAcessoResult == 1) {
             write_log("Sucesso na autenticação RM (via método) para usuário: {$username}");
             return true;
         } else {
              // Captura a resposta completa em caso de falha para análise
              $lastResponse = $soapClientMethodAuth->__getLastResponse(); // Requer 'trace' => true
              write_log("Falha na autenticação RM para usuário: {$username}. Resposta: " . ($lastResponse ?: 'N/A'), ['result' => $resultMethodAuth]);
              // Tentar decodificar a resposta se for XML
              $errorMessage = "Credenciais inválidas ou erro inesperado.";
              if ($lastResponse) {
                  // Tenta extrair uma mensagem de erro mais específica do XML/SOAP Fault
                  // (Implementação depende da estrutura da resposta de erro do RM)
              }
              return $errorMessage;
         }

 }

 function buscaStatusUsuario($codUsuario, $codColigada = 0, $config) { // Mudamos o nome talvez
    $function = "RealizarConsultaSQL";

    // --- Informações recebidas do admin ---
    $codSentenca = "OPEN.INTEG.0001"; // A sentença que ele passou
    $codSistema = "S";                  // O sistema que ele informou
    // Monta a string de parâmetros conforme ele informou
    $parameters = "P_CODUSUARIO=" . $codUsuario . ";P_CODCOLIGADA=" . $codColigada;
    // --- Fim ---

    $xmlRecord = array(
        "RealizarConsultaSQL" => array(
            "codSentenca" => $codSentenca, // Variável definida acima
            "codColigada" => $codColigada, // Variável definida acima (ou 0)
            "codSistema"  => $codSistema,  // Variável definida acima
            "parameters"  => ''   // Variável definida acima
        )
    );
    $basicParams = array(
        'trace'=> TRUE,
        'login' => $config['user'],
        'password' => $config['pass'],
        'exceptions'=>true,
        'cache_wsdl'=> WSDL_CACHE_NONE // Importante para dev
    );
 

    $clientSoapWsConsultaSql = new \SoapClient($config["hostTbc"] . 'wsConsultaSQL/MEX?WSDL', $basicParams);

     $optionSoapWsConsultaSql = array('location' => $config["hostTbc"] . 'wsConsultaSQL/IwsConsultaSQL');
        $result = $clientSoapWsConsultaSql->__soapCall($function, $xmlRecord, $optionSoapWsConsultaSql);
        print_r($result);
        exit;
        if (empty($result->RealizarConsultaSQLResult)) { /*...*/ return null; }

        $xml = simplexml_load_string($result->RealizarConsultaSQLResult);

        // --- Leitura do resultado conforme ele informou ---
        $nomeColunaStatus = "STATUS_USR"; // Nome da coluna que ele informou
        $valorAtivo = "A";               // Valor para ativo que ele informou

        if ($xml === false || !isset($xml->Resultado->$nomeColunaStatus)) {
            /*...*/ return null;
        }

        $statusUsuario = (string)$xml->Resultado->$nomeColunaStatus;

        // Retornar os dados relevantes, talvez um array
        $dadosRetorno = [
            'status' => ($statusUsuario == $valorAtivo), // true se ativo, false se inativo
            'ra' => isset($xml->Resultado->RA_ALUNO) ? (string)$xml->Resultado->RA_ALUNO : null,
            'idps' => isset($xml->Resultado->ID_PROCESSO) ? (int)$xml->Resultado->ID_PROCESSO : null
            // Adicionar outros campos que a consulta retornar
        ];
        return $dadosRetorno;
        // --- Fim ---
 
}

 function isUsuarioAtivo($identificadorUsuario, $config, $codColigada = 0) {

    $function = "RealizarConsultaSQL";

    // --- Valores REAIS do seu RM ---
    $codSentenca = "PORTAL.USER.STATUS"; // Código REAL da sentença
    $parameters = "LOGIN=" . $identificadorUsuario; // Parâmetro REAL esperado pela sentença
    $codSistema = "S"; // Sistema REAL da sentença
    // --- Fim dos valores ---

    $xmlRecord = array(
        "RealizarConsultaSQL" => array(
            "codSentenca" => $codSentenca,
            "codColigada" => $codColigada,
            "codSistema" => $codSistema,
            "parameters" => $parameters
        )
    );
    $basicParams = array(
        'trace'=> TRUE,
        'login' => $config['user'],
        'password' => $config['pass'],
        'exceptions'=>true,
        'cache_wsdl'=> WSDL_CACHE_NONE // Importante para dev
    );
 
    $optionSoapWsConsultaSql = array('location' => $config["hostTbc"] . 'wsConsultaSQL/IwsConsultaSQL');
    $clientSoapWsConsultaSql = new \SoapClient($config["hostTbc"] . 'wsConsultaSQL/MEX?WSDL', $basicParams);
    // try {
        // Use o cliente SOAP correto para Consulta SQL
        return $clientSoapWsConsultaSql->__soapCall($function, $xmlRecord, $optionSoapWsConsultaSql);
      return  $result = $this->_clientSoapWsConsultaSql->__soapCall($function, $xmlRecord, $optionSoapWsConsultaSql);


        
    $function = "RealizarConsultaSQL";
    $codSentenca = "[CODIGO_DA_SUA_SENTENCA_SQL]"; 
    $parameters = "[NOME_PARAMETRO_USUARIO]=" . $identificadorUsuario . ";CODCOLIGADA=" . $codColigada; // Ex: "CODUSUARIO=".$identificadorUsuario.";CODCOLIGADA=".$codColigada

    // Defina o código do sistema (ex: 'S', 'G', 'P', etc.)
    $codSistema = "[CODIGO_SISTEMA]"; // Ex: 'S'
     // --- Fim [SUA_INFORMACAO] ---


    $xmlRecord = array(
        "RealizarConsultaSQL" => array(
            "codSentenca" => $codSentenca,
            "codColigada" => $codColigada, // Ou a coligada correta, se não for 0
            "codSistema" => $codSistema,
            "parameters" => $parameters
        )
    );
    $basicParams = array(
        'trace'=> TRUE,
        'login' => $config['user'],
        'password' => $config['pass'],
        'exceptions'=>true,
        'cache_wsdl'=> WSDL_CACHE_NONE // Importante para dev
    );
 
    $optionSoapWsConsultaSql = array('location' => $config["hostTbc"] . 'wsConsultaSQL/IwsConsultaSQL');
    $clientSoapWsConsultaSql = new \SoapClient($config["hostTbc"] . 'wsConsultaSQL/MEX?WSDL', $basicParams);
    // try {
        // Use o cliente SOAP correto para Consulta SQL
        $result = $clientSoapWsConsultaSql->__soapCall($function, $xmlRecord, $optionSoapWsConsultaSql);

        if (empty($result->RealizarConsultaSQLResult)) {
            error_log("Consulta de status para usuario " . $identificadorUsuario . " retornou vazio.");
            return null; // Usuário não encontrado ou consulta vazia
        }

        $xml = simplexml_load_string($result->RealizarConsultaSQLResult);
    print_r( $xml );
    exit;
    
         // --- Fim [SUA_INFORMACAO] ---

    // } catch (\SoapFault $e) {
    //     // Erro na chamada SOAP ao consultar status
    //      error_log("SoapFault ao consultar status do usuario " . $identificadorUsuario . ": " . $e->getMessage());
    //      return null; // Indica erro na consulta
    // } catch (\Exception $e) {
    //     // Outro erro
    //     error_log("Exception ao consultar status do usuario " . $identificadorUsuario . ": " . $e->getMessage());
    //     return null; // Indica erro na consulta
    // }
}

 

 
 // Obter credenciais (simulado - em um cenário real, viria do POST/GET da interface CAS)
 $input_user = $_POST['usuario'] ?? 'teste_user'; // Use ?user=seu_usuario&pass=sua_senha na URL para testar
 $input_pass = $_POST['senha'] ?? 'senha_incorreta'; // ou data de nascimento para fallback
 
 echo "<h1>Teste de Autenticação SSO</h1>";
 echo "<p>Usuário: " . htmlspecialchars($input_user) . "</p>";
 
//  $statusAtivo = isUsuarioAtivo($input_user,$config['wstotvs']); // Usando o $usuario como identificador

   $buscaStatusUsuario = buscaStatusUsuario($input_user, 0, $config['wstotvs']);
   print_r(['buscaStatusUsuario' =>  $buscaStatusUsuario]);
   exit;

 $result = check_user_exists_rm(
    $input_user,
    $config['wstotvs'],
    $config['consulta_usuario']
);
 print_r(['Resultado_Funcao_Verificar_Usuario' =>  $result]);
  
  $login = login($input_user, $input_pass, $config['wstotvs']);

print_r(['Resultado_Funcao_Login' => $login]);

 
 $rm_auth_result = authenticate_rm($input_user, $input_pass, $config['wstotvs']);
 print_r(['Resultado_Funcao_autenticacao' => $rm_auth_result]);
 exit;

 if ($rm_auth_result === true) {
     echo "<p style='color:green;'>Autenticação no Totvs RM BEM-SUCEDIDA!</p>";
     // Aqui, o servidor CAS geraria o ticket e redirecionaria de volta ao OpenLMS
     write_log("AUTH_SUCCESS (RM)", ['user' => $input_user]);
 
 } else {
     echo "<p style='color:orange;'>Autenticação no Totvs RM FALHOU ou serviço indisponível.</p>";
     echo "<p>Erro RM: " . htmlspecialchars($rm_auth_result) . "</p>";
     write_log("AUTH_FAIL (RM)", ['user' => $input_user, 'reason' => $rm_auth_result]);
 
     // 2. Tentar autenticação local (fallback)
     echo "<p>Tentando autenticação local (fallback)...</p>";
     // Para o fallback, a senha esperada é a data de nascimento
     // Em um cenário real, você pediria a data de nascimento ou teria uma lógica
     // para saber quando usar a senha normal e quando usar a data de nascimento.
     // Aqui, vamos assumir que $input_pass PODE ser a data de nascimento.
     $local_auth_result = authenticate_local($input_user, $input_pass, $config['local_db']);
 
     if ($local_auth_result === true) {
         echo "<p style='color:green;'>Autenticação LOCAL BEM-SUCEDIDA!</p>";
          // Aqui, o servidor CAS geraria o ticket e redirecionaria de volta ao OpenLMS
          write_log("AUTH_SUCCESS (Local)", ['user' => $input_user]);
     } else {
         echo "<p style='color:red;'>Autenticação LOCAL FALHOU.</p>";
          // Aqui, o servidor CAS retornaria erro ou redirecionaria para página de falha
          write_log("AUTH_FAIL (Local)", ['user' => $input_user]);
          echo "<p>Acesso negado.</p>";
     }
 }
 
 echo "<hr>";
 echo "<h2>Log de Eventos Recentes:</h2>";
 echo "<pre>";
 // Mostra as últimas linhas do log para facilitar o debug
 $logContent = file_exists($config['log_file']) ? file_get_contents($config['log_file']) : 'Arquivo de log não encontrado.';
 $logLines = explode("\n", trim($logContent));
 echo htmlspecialchars(implode("\n", array_slice($logLines, -10))); // Mostra as últimas 10 linhas
 echo "</pre>";
 
 exit;
