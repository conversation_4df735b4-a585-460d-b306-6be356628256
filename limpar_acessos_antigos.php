<?php
$db = new SQLite3('acessos.db');

// Data de 6 meses atrás (formato compatível com SQLite: YYYY-MM-DD)
$dataLimite = date('Y-m-d', strtotime('-6 months'));

// Executa a exclusão
$sql = "DELETE FROM acessos WHERE DATE(created_at) < DATE(:data)";
$stmt = $db->prepare($sql);
$stmt->bindValue(':data', $dataLimite);
$result = $stmt->execute();

echo "Registros com mais de 6 meses foram excluídos com sucesso.";
?>
