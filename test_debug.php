<?php

/**
 * Teste simples para verificar se o sistema de debug está funcionando
 */

// Incluir apenas as funções de debug
function writeDebugLog($message, $startTime = null, $operation = 'GERAL')
{
    $logFile = __DIR__ . '/debug_login.log';
    $timestamp = date('Y-m-d H:i:s.u');
    $executionTime = '';

    if ($startTime !== null) {
        $endTime = microtime(true);
        $duration = ($endTime - $startTime) * 1000; // Converter para milissegundos
        $executionTime = sprintf(" [TEMPO: %.2f ms]", $duration);
    }

    $logEntry = "[{$timestamp}] [{$operation}] {$message}{$executionTime}" . PHP_EOL;

    // Tentar escrever no arquivo de log
    if (file_put_contents($logFile, $logEntry, FILE_APPEND | LOCK_EX) === false) {
        error_log("ERRO: Não foi possível escrever no arquivo debug_login.log");
    }
}

function startTimer($operation = 'OPERACAO')
{
    $startTime = microtime(true);
    writeDebugLog("INÍCIO - {$operation}", null, 'TIMER');
    return $startTime;
}

function endTimer($startTime, $operation = 'OPERACAO', $additionalInfo = '')
{
    $info = $additionalInfo ? " - {$additionalInfo}" : '';
    writeDebugLog("FIM - {$operation}{$info}", $startTime, 'TIMER');
}

echo "=== TESTE DO SISTEMA DE DEBUG ===\n";

// Limpar o arquivo de log para o teste
$logFile = __DIR__ . '/debug_login.log';
file_put_contents($logFile, '');

echo "1. Testando função writeDebugLog...\n";
writeDebugLog("Teste de log simples", null, 'TESTE');

echo "2. Testando startTimer e endTimer...\n";
$timer = startTimer("TESTE_TIMER");
usleep(100000); // Simular 100ms de processamento
endTimer($timer, "TESTE_TIMER", "Simulação de 100ms");

echo "3. Testando timer aninhado...\n";
$timerPrincipal = startTimer("OPERACAO_PRINCIPAL");
$timerSecundario = startTimer("OPERACAO_SECUNDARIA");
usleep(50000); // 50ms
endTimer($timerSecundario, "OPERACAO_SECUNDARIA", "Operação interna");
usleep(50000); // mais 50ms
endTimer($timerPrincipal, "OPERACAO_PRINCIPAL", "Operação completa");

echo "4. Verificando conteúdo do arquivo de log...\n";
if (file_exists($logFile)) {
    $logContent = file_get_contents($logFile);
    if (!empty($logContent)) {
        echo "✓ Arquivo de log criado com sucesso!\n";
        echo "Conteúdo do log:\n";
        echo "================\n";
        echo $logContent;
        echo "================\n";
    } else {
        echo "✗ Arquivo de log está vazio!\n";
    }
} else {
    echo "✗ Arquivo de log não foi criado!\n";
}

echo "\n=== TESTE CONCLUÍDO ===\n";
