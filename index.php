<?php
  


  define('ENVIRONMENT', 'development'); 
  session_start();
  if (defined('ENVIRONMENT'))
  {
      switch (ENVIRONMENT)
      {
          case 'development':
  
              error_reporting(0);
              ini_set('display_errors', 'on');
              ini_set('display_startup_errors', 1);
              error_reporting(E_ALL);
  
              $url_integracao_sso_lms = "https://moodlesso.trevisan.edu.br/homologacao?origin=app"; 
              $moodle_sandbox_login_action_url = "https://faculdadetrevisan-sandbox.mrooms.net/login/index.php";
  
              $config = array(
                  'wstotvs' => array(
                      'hostTbc' => 'https://faculdadetrevisan150171.rm.cloudtotvs.com.br:2106/',
                      'user' => 'edvaldoribeiro.pvt',
                      'pass' => 'Picanha@2023',
                      'tbcType' => 'host',
                      'urlsRequisicaoPermitidas' => array('http://localhost/','https://estudante.trevisan.edu.br/', 'https://homologa.estudante.trevisan.edu.br:2104/', 'https://homologa.estudante.trevisan.edu.br/')
                  ),
                  'wstotvsrest' => array(
                      'host' => 'https://faculdadetrevisan150171.rm.cloudtotvs.com.br',
                      'port' => '2106',
                      'user' => 'edvaldoribeiro.pvt',  
                      'pass' => 'Picanha@2023'
                  ),
      
                  'lms' => [
                      'url' => 'https://faculdadetrevisan-sandbox.mrooms.net/',  
                      'tokenws' => '2c9cddea435fc873118e4f9c420a8f6a',  
                      'tokenwspvt' => '0040cd37-6582-101c-e683-b6b2e11a277a'  
                  ]
              );
  
              define('CONFIG_GLOBAL', $config);
              define('URL_POST', 'https://faculdadetrevisan-sandbox.mrooms.net/login/index.php');
              define('URL', 'https://faculdadetrevisan-sandbox.mrooms.net');
          break;
  
          case 'production':
              error_reporting(0);
              ini_set('display_errors', 0);
  
              $url_integracao_sso_lms = "https://moodlesso.trevisan.edu.br?origin=app"; 
              $moodle_sandbox_login_action_url = "https://faculdadetrevisan.mrooms.net/login/index.php";
  
              $config = array(
                  'wstotvs' => array(
                      'hostTbc' => 'https://faculdadetrevisan150170.rm.cloudtotvs.com.br:8051/',
                      'user' => 'edvaldoribeiro.pvt',
                      'pass' => 'Picanha@2023',
                      'tbcType' => 'host',
                      'urlsRequisicaoPermitidas' => array('http://localhost/','https://estudante.trevisan.edu.br/', 'https://homologa.estudante.trevisan.edu.br:2104/', 'https://homologa.estudante.trevisan.edu.br/')
                  ),
                  'wstotvsrest' => array(
                      'host' => 'https://faculdadetrevisan150170.rm.cloudtotvs.com.br',
                      'port' => '8051',
                      'user' => 'edvaldoribeiro.pvt',  
                      'pass' => 'Picanha@2023'
                  ),
 
                  'lms' => [
                      'url' => 'https://faculdadetrevisan.mrooms.net/',  
                      'tokenws' => '2c9cddea435fc873118e4f9c420a8f6a',  
                      'tokenwspvt' => '0040cd37-6582-101c-e683-b6b2e11a277a'  
                  ]
              );
  
              define('CONFIG_GLOBAL', $config);
              define('URL_POST', 'https://faculdadetrevisan.mrooms.net/login/index.php');
              define('URL', 'https://faculdadetrevisan.mrooms.net');
  
          break;
  
          default:
              exit('The application environment is not set correctly.');
      }
  }
   
  date_default_timezone_set('America/Sao_Paulo');
   
  $parametro_username_na_url_sso = "u"; 
   
  class WSMoodle{
      private $_config;
      private $_dbintegration;
      private $_baseUrlWS;
      private $_baseUrlWSPvt;
      private $_curl;
      private $_course;
      private $_msg;
      private $_baseUrlWSConduitCourse;
      private $_baseUrlWSConduitUser;
      private $_baseUrlWSConduitEnrol;
  
      // ADICIONADO: Atributos para o serviço REST padrão do Moodle
      private $_restToken;
      private $_restApiUrl;
  
  
      public function __construct(){
          // MODIFICADO: Acessa $config do escopo global. Removido include('config.php') redundante.
          global $config; 
          $this->_config = $config["lms"]; 
  
          include_once('Curl.class.php'); 
  
          $this->_baseUrlWSConduitUser = $this->_config["url"] . '/blocks/conduit/webservices/rest/user.php'. '?token=' .  $this->_config["tokenwspvt"];
          
          // ADICIONADO: Inicializa URL da API REST e token
          $this->_restApiUrl = $this->_config["url"] . 'webservice/rest/server.php';
          $this->_restToken = $this->_config["tokenws"];
  
          $this->_curl = new Curl(); 
      }
  
      public function createUserConduit($xml){
          $method = 'handle';
          $body = '&method='. $method . '&xml='. $xml;
          $serverurl = $this->_baseUrlWSConduitUser;
          $respoXml = $this->_curl->post($serverurl, $body);
          $resp = simplexml_load_string($respoXml);
          return $resp;
      }
  
      // ADICIONADO: Função _callRestApi para realizar chamadas REST ao Moodle
      private function _callRestApi($functionName, $params) {
          $requestBody = http_build_query($params);
  
          $serverUrl = $this->_restApiUrl . '?' . http_build_query([
              'wstoken'               => $this->_restToken,
              'wsfunction'            => $functionName,
              'moodlewsrestformat'    => 'json'
          ]);
  
          try {
              $responseJson = $this->_curl->post($serverUrl, $requestBody);
              $response = json_decode($responseJson);
  
              if (json_last_error() !== JSON_ERROR_NONE) {
                  error_log("WSMoodle Error: Invalid JSON response from Moodle API for function {$functionName}. Response: {$responseJson}");
                  return null;
              }
  
              if (isset($response->exception)) {
                  error_log("WSMoodle Error from Moodle API ({$functionName}): " . $response->message . " | Debug Info: " . (isset($response->debuginfo) ? $response->debuginfo : 'N/A'));
                  // Você pode querer lançar uma exceção ou retornar o objeto de erro diretamente aqui
              }
              return $response;
          } catch (\Exception $e) {
              error_log("WSMoodle Error calling {$functionName}: " . $e->getMessage());
              return null;
          }
      }
  
       /**
       * Verifica se um usuário existe no Moodle pelo campo especificado.
       *
       * @param string $field O campo pelo qual buscar (ex: 'username', 'email', 'idnumber').
       * @param string $value O valor do campo a ser buscado.
       * @return array|null Retorna um array de objetos de usuário se encontrado, um array vazio se não, ou null em caso de erro na comunicação.
       */
      public function userExistsInMoodle(string $field, string $value)
      {
          $functionName = 'core_user_get_users_by_field';
          $params = [
              'field' => $field,
              'values' => [$value]
          ];
  
          $response = $this->_callRestApi($functionName, $params);
  
          if (is_null($response) || isset($response->exception)) {
              return null; // Indica erro na busca ou resposta inválida
          }
  
          return $response; // Retorna o array de usuários (pode ser vazio se não encontrado)
      }
  
      // ADICIONADO: Função upsertUser para criar ou atualizar usuários no Moodle
      /**
       * Cria ou atualiza um usuário no Moodle.
       * Primeiro verifica se o usuário existe pelo 'idnumber' (recomendado para sincronização).
       * Se existir, atualiza. Se não, cria.
       *
       * @param array $userData Array associativo com os dados do usuário (username, password, firstname, lastname, email, department, idnumber).
       * @return object|null Retorna o objeto de resposta da API do Moodle (sucesso ou erro), ou null em caso de falha.
       */
      public function upsertUser($userData) {
          // Passo 1: Verificar se o usuário já existe usando o 'idnumber' para garantir unicidade e consistência.
          // Se o 'idnumber' não for confiável ou único, use 'username' ou 'email'.
          $existingUsers = $this->userExistsInMoodle('username', $userData['username']);
  
          if (is_null($existingUsers)) {
              error_log("WSMoodle upsertUser: Erro na verificação inicial de existência do usuário.");
              return null; // Falha na busca inicial
          }
  
          if (!empty($existingUsers)) {
              // Passo 2: O usuário EXISTE. Vamos atualizá-lo.
              $userId = $existingUsers[0]->id; // Pega o ID do usuário Moodle
  
              $updateParams = [
                  'users' => [
                      [
                          'id'        => $userId,
                          'username'  => $userData['username'], // Atualiza username também, se necessário
                          'password'  => $userData['password'],
                          'firstname' => $userData['firstname'],
                          'lastname'  => $userData['lastname'],
                          'email'     => $userData['email'],
                          'department'=> $userData['department'],
                          'suspended' => 0 // Garante que a conta esteja ativa
                      ]
                  ]
              ];
              
              $result = $this->_callRestApi('core_user_update_users', $updateParams);
              return $result; // Retorna o objeto de resposta da API (sucesso ou erro)
  
          } else {
              // Passo 3: O usuário NÃO EXISTE. Vamos criá-lo.
            //   $createParams = [
            //       'users' => [
            //           [
            //               'username'  => $userData['username'],
            //               'idnumber'  => $userData['idnumber'], // Importante para futuras sincronizações
            //               'password'  => $userData['password'],
            //               'firstname' => $userData['firstname'],
            //               'lastname'  => $userData['lastname'],
            //               'email'     => $userData['email'],
            //               'department'=> $userData['department'],
            //               'auth'      => 'manual' // Define o método de autenticação padrão. 'manual' ou 'db' é comum para SSO.
            //           ]
            //       ]
            //   ];
  
            //   $result = $this->_callRestApi('core_user_create_users', $createParams);
            //   return $result; // Retorna o objeto de resposta da API (sucesso ou erro)
          }
      }
  }

// define('ENVIRONMENT', 'development'); 
// session_start();
// if (defined('ENVIRONMENT'))
// {
// 	switch (ENVIRONMENT)
// 	{
// 		case 'development':

// 		    error_reporting(0);
// 			ini_set('display_errors', 'on');
// 			ini_set('display_startup_errors', 1);
// 			error_reporting(E_ALL);

//             $url_integracao_sso_lms = "https://moodlesso.trevisan.edu.br/homologacao?origin=app"; 
//             $moodle_sandbox_login_action_url = "https://faculdadetrevisan-sandbox.mrooms.net/login/index.php";

//             $config = array(
//                 'wstotvs' => array(
//                     'hostTbc' => 'https://faculdadetrevisan150171.rm.cloudtotvs.com.br:2106/',
//                     'user' => 'edvaldoribeiro.pvt',
//                     'pass' => 'Picanha@2023',
//                     'tbcType' => 'host',
//                     'urlsRequisicaoPermitidas' => array('http://localhost/','https://estudante.trevisan.edu.br/', 'https://homologa.estudante.trevisan.edu.br:2104/', 'https://homologa.estudante.trevisan.edu.br/')
//                 ),
//                 'wstotvsrest' => array(
//                     'host' => 'https://faculdadetrevisan150171.rm.cloudtotvs.com.br',
//                     'port' => '2106',
//                     'user' => 'edvaldoribeiro.pvt',  
//                     'pass' => 'Picanha@2023'
//                 )
//             );

//             define('CONFIG_GLOBAL', $config);
//             define('URL_POST', 'https://faculdadetrevisan-sandbox.mrooms.net/login/index.php');
//             define('URL', 'https://faculdadetrevisan-sandbox.mrooms.net');
// 		break;

// 		case 'production':
// 			error_reporting(0);
//             ini_set('display_errors', 0);

//             $url_integracao_sso_lms = "https://moodlesso.trevisan.edu.br?origin=app"; 
//             $moodle_sandbox_login_action_url = "https://faculdadetrevisan.mrooms.net/login/index.php";

//             $config = array(
//                 'wstotvs' => array(
//                     'hostTbc' => 'https://faculdadetrevisan150170.rm.cloudtotvs.com.br:8051/',
//                     'user' => 'edvaldoribeiro.pvt',
//                     'pass' => 'Picanha@2023',
//                     'tbcType' => 'host',
//                     'urlsRequisicaoPermitidas' => array('http://localhost/','https://estudante.trevisan.edu.br/', 'https://homologa.estudante.trevisan.edu.br:2104/', 'https://homologa.estudante.trevisan.edu.br/')
//                 ),
//                 'wstotvsrest' => array(
//                     'host' => 'https://faculdadetrevisan150170.rm.cloudtotvs.com.br',
//                     'port' => '8051',
//                     'user' => 'edvaldoribeiro.pvt',  
//                     'pass' => 'Picanha@2023'
//                 )
//             );

//             define('CONFIG_GLOBAL', $config);
//             define('URL_POST', 'https://faculdadetrevisan.mrooms.net/login/index.php');
//             define('URL', 'https://faculdadetrevisan.mrooms.net');

// 		break;

// 		default:
// 			exit('The application environment is not set correctly.');
// 	}
// }

 
// date_default_timezone_set('America/Sao_Paulo');
 
// $parametro_username_na_url_sso = "u"; 
 
// class WSMoodle{
// 	private $_config;
// 	private $_dbintegration;
// 	private $_baseUrlWS;
// 	private $_baseUrlWSPvt;
// 	private $_curl;
// 	private $_course;
// 	private $_msg;
// 	private $_baseUrlWSConduitCourse;
// 	private $_baseUrlWSConduitUser;
// 	private $_baseUrlWSConduitEnrol;

// 	public function __construct(){
// 		//include('config.php');
// 		include('Curl.class.php');

//         $this->_config = $config["lms"];
//         $this->_baseUrlWSConduitUser = $this->_config["url"] . '/blocks/conduit/webservices/rest/user.php'. '?token=' .  $this->_config["tokenwspvt"];
//         $this->_curl = new Curl();
// 	}

// 	public function createUserConduit($xml){
// 		$method = 'handle';
// 		$body = '&method='. $method . '&xml='. $xml;
// 		$serverurl = $this->_baseUrlWSConduitUser;
// 		$respoXml = $this->_curl->post($serverurl, $body);
// 		$resp = simplexml_load_string($respoXml);
// 		return $resp;
// 	}

// }



class WSTotvs {
    private $_basicParams;
    private $_optionSoapLogin;
    private $_locationLogin;
    private $_clientSoapWsConsultaSql;
    private $_userTotvs;
    private $_passwordTotvs;
    private $_locationWsConsultaSql;
    private $_optionSoapWsConsultaSql;
    private $_config = CONFIG_GLOBAL['wstotvs'];

    public function __construct() {
        $this->_userTotvs = $this->_config["user"];
        $this->_passwordTotvs = $this->_config["pass"];
        $this->_basicParams = array('trace'=> TRUE, 'login' => $this->_userTotvs ,'password' => $this->_passwordTotvs, 'exceptions'=>true);
        $this->_clientSoapWsConsultaSql = new \SoapClient($this->_config["hostTbc"] . 'wsConsultaSQL/MEX?WSDL', $this->_basicParams);

        $this->_basicParams = array(
            'trace'       => TRUE,
            'login'       => $this->_config["user"],
            'password'    => $this->_config["pass"],
            'exceptions'  => true,
            'cache_wsdl'  => WSDL_CACHE_NONE 
        );
        $this->_locationLogin = $this->_config["hostTbc"] . 'wsDataServer/IwsBase';
        ini_set('soap.wsdl_cache_enabled', 0);
        ini_set('soap.wsdl_cache_ttl', 0);
        $this->_optionSoapLogin = array('location' => $this->_locationLogin);
        try {
             new \SoapClient($this->_config["hostTbc"] . 'wsDataServer/MEX?WSDL', $this->_basicParams);
        } catch (\SoapFault $e) {
             error_log("Erro Crítico SOAP no construtor WSTotvs: " . $e->getMessage());
             throw new \Exception("Falha ao carregar definição do serviço RM (WSDL). Verifique a URL ou conectividade: " . $e->getMessage(), 0, $e);
        } catch (\Exception $e) {
             error_log("Erro geral no construtor WSTotvs: " . $e->getMessage());
             throw $e;
        }
        $this->_locationWsConsultaSql = $this->_config["hostTbc"] . 'wsConsultaSQL/IwsConsultaSQL';

        $this->_optionSoapWsConsultaSql = array('location' => $this->_locationWsConsultaSql); 
        
    }

    public function registrarLogs($user, $response = 'Autenticado com sucesso', $tipo = 'login', $status = 'OK'){
        $db_path = __DIR__ . '/acessos.db';  

        if (!is_writable(__DIR__)) {
            error_log("ERRO DE PERMISSÃO: O diretório '" . __DIR__ . "' não tem permissão de escrita para criar/acessar 'acessos.db'.");
            return false;  
        }

        try {
            $db = new SQLite3($db_path);

            $re =   $db->exec('CREATE TABLE IF NOT EXISTS acessos (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    username TEXT NOT NULL,
                    ip TEXT NOT NULL,
                    tipo TEXT NOT NULL,
                    response TEXT,
                    status TEXT NOT NULL,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )');

            $username = $user;
            $ip = $_SERVER['REMOTE_ADDR'] ?? '127.0.0.1';
            $created_at = date('Y-m-d H:i:s');

            $stmt = $db->prepare("INSERT INTO acessos (username, ip, tipo, response, status, created_at) VALUES (?, ?, ?, ?, ?, ?)");
            $stmt->bindValue(1, $username);
            $stmt->bindValue(2, $ip);
            $stmt->bindValue(3, $tipo);
            $stmt->bindValue(4, $response);
            $stmt->bindValue(5, $status);
            $stmt->bindValue(6, $created_at);

            $result = $stmt->execute();


            $db->close();  
            return $result;

        } catch (\Exception $e) {
 
            error_log("ERRO AO REGISTRAR LOG: " . $e->getMessage());
            return false;
        }
    }

    public function buscaNomeBase(){
        $function = "RealizarConsultaSQL";
        $xmlRecord = array(
            "RealizarConsultaSQL" => array(
                "codSentenca" => "AGEND.0031",
                "codColigada" => "0",
                "codSistema" => "S",
                "parameters" => ""
            )
        );
        try {
            $result = $this->_clientSoapWsConsultaSql->__soapCall($function, $xmlRecord, $this->_optionSoapWsConsultaSql);
            $xml = new \SimpleXMLElement($result->RealizarConsultaSQLResult);
            return (array)$xml;
        } catch (\Exception $e) {
            return $e;           
        }
    }

    public function getEmployeeId($codUsuario){
        $function = "RealizarConsultaSQL";
        $xmlRecord = array(
            "RealizarConsultaSQL" => array(
                "codSentenca" => "DEGREED.001",
                "codColigada" => "0",
                "codSistema" => "S",
                "parameters" => "CODUSUARIO=".$codUsuario
            )
        );
        try {
            $result = $this->_clientSoapWsConsultaSql->__soapCall($function, $xmlRecord,   array('location' => $this->_config["hostTbc"] . "/wsConsultaSQL/IwsConsultaSQL")) ;
            // var_dump( $result);
            // exit;
            //$xml = new \SimpleXMLElement($result->RealizarConsultaSQLResult);
            $xml = simplexml_load_string($result->RealizarConsultaSQLResult);
            $employeeid = (array)$xml->Resultado->employeeid;
            return $employeeid[0];
        } catch (\Exception $e) {
            return $e;           
        }
    }


    public function buscaCursoMatriculado($cpf){
        echo ' cpf '.$cpf;
        $function = "RealizarConsultaSQL";
        $xmlRecord = array(
            "RealizarConsultaSQL" => array(
                "codSentenca" => "PS.FICHA.002",
                "codColigada" => "0",
                "codSistema" => "S",
                "parameters" => "CPF=".$cpf
            )
        );
 
       return $result = $this->_clientSoapWsConsultaSql->__soapCall($function, $xmlRecord, array('location' => $this->_config["hostTbc"] . "/wsConsultaSQL/IwsConsultaSQL"));
     
        $xml = simplexml_load_string($result->RealizarConsultaSQLResult);

        return $xml;
    }

    public function buscaDadosMatricula($codColigada, $idPs, $numeroInscricao){
        echo 'inicio';
        $function = "RealizarConsultaSQL";
        $xmlRecord = array(
            "RealizarConsultaSQL" => array(
                "codSentenca" => "PS.DOCXPRESS.001",
                "codColigada" => "0",
                "codSistema" => "S",
                "parameters" => "CODCOLIGADA=".$codColigada.";IDPS=".$idPs.";NUMEROINSCRICAO=".$numeroInscricao
            )
        );

        $result = $this->_clientSoapWsConsultaSql->__soapCall($function, $xmlRecord, array('location' => $this->_config["hostTbc"] . "/wsConsultaSQL/IwsConsultaSQL"));
        // var_dump($result);
        // exit;
        $xml = simplexml_load_string($result->RealizarConsultaSQLResult);

        return $xml->Resultado;
    }

    public function Ping(){

        $CODCOLIGADA = 1;
        $RA =  '';
        $IDPERLET =  '';
        $IDHABILITACAOFILIAL =  '';
        require_once("includes/WSTotvsRest.class.php");
        $totvs = new WSTotvsRest();
 
        return $totvs->callConsultaSQL("1", "S", "PING", "CODCOLIGADA=".$CODCOLIGADA.";RA=".$RA.";IDPERLET=".$IDPERLET.";IDHABILITACAOFILIAL=".$IDHABILITACAOFILIAL);
    }

    public function login($username, $password): bool {
        $function = "AutenticaAcesso";
        $xmlRecord = array("AutenticaAcesso" => array());
        $loginParams = array(
            'trace'       => TRUE,
            'login'       => $username,
            'password'    => $password,    
            'exceptions'  => true, 
            'cache_wsdl'  => WSDL_CACHE_NONE
        );
        $wsdlUrl = $this->_config["hostTbc"] . 'wsDataServer/MEX?WSDL';
        try {
            $clientLoginSoap = new \SoapClient($wsdlUrl, $loginParams);
            $r = $clientLoginSoap->__soapCall($function, $xmlRecord, $this->_optionSoapLogin);
 
            $this->registrarLogs($username, true);
            return true;
        } catch (\SoapFault $sf) {
            $this->registrarLogs($username, $sf->getMessage(), 'login', $sf->faultcode);
            error_log("SoapFault no método login para usuário '$username': " . $sf->getMessage() . " | Fault Code: " . $sf->faultcode . " | Fault String: " . $sf->faultstring);
            return false;
        } catch (\Exception $e) {
            $this->registrarLogs($username, $e->getMessage(), 'login', $e->faultcode);
             error_log("Exception no método login para usuário '$username': " . $e->getMessage());
             throw $e;
        }
    }
}

function alterarSenhaMoodle($username, $password, $email = null, $firstname = null, $lastname = null, $department = null, )
{   
   /*
            $xml = '<?xml version="1.0" encoding="UTF-8"?>
            <data>
                <datum action="update">
                    <mapping name="username">' . $username . '</mapping>
                    <mapping name="idnumber">' . $username . '</mapping>
                    <mapping name="suspended">0</mapping>
                    <mapping name="email">' . $email . '</mapping>
                    <mapping name="password">' . $password . '</mapping>
                    <mapping name="firstname">' . $firstname . '</mapping>
                    <mapping name="lastname">' . $lastname . '</mapping>
                    <mapping name="department">' . $department . '</mapping>
                </datum>
            </data>';
        $moodle = new WSMoodle();
        return $moodle->createUserConduit($xml);
          */
}
function autenticarRmERedirecionarLms($username, $password, $lmsSsoUrl, $urlParamName = 'u'): bool {
    if (empty($username) || $password === null || empty($lmsSsoUrl)) {
         error_log("Erro: Usuário, password ou URL do LMS não fornecidos para autenticarRmERedirecionarLms.");
         return false;
    }
    $moodle = new WSMoodle();
    $wsTotvs = new WSTotvs();

   //  try {
       // throw new \Exception("teste");

        
        $autenticadoRM = $wsTotvs->login($username, $password);
        // echo '----';
        // print_r($autenticadoRM);
        // echo '----';
     
        // exit;

        $_SESSION['usuario'] = $username;
        $_SESSION['senha'] = $password;
        if (!empty($autenticadoRM)) {
          
            $userParamValue = base64_encode($username); // Usuário do TOTVS
            $passParamValue = base64_encode($password);
       
            $separator = (strpos($lmsSsoUrl, '?') === false) ? '?' : '&';
            $redirectUrl = $lmsSsoUrl . $separator . rawurlencode($urlParamName) . '=' . rawurlencode($userParamValue). '&p='.rawurlencode($passParamValue);
            if (ob_get_level() > 0) {
                 ob_end_clean();
            }
            // print_r($redirectUrl);
            // exit;
            header("Location: " . $redirectUrl);
            exit;
        } else {
    
            $wsTotvs->registrarLogs($user, 'Usuário não existe no TOTVS', 'login', 'ALERT');
            

            $res_moodle = $moodle->userExistsInMoodle('username',$_POST['username']);
 
            if (!empty($res_moodle) && !empty($res_moodle[0]->username)) {

                $userParamValue = base64_encode($res_moodle[0]->username); // Usuário do MOODLE
                $passParamValue =  base64_encode($password);
           
                $separator = (strpos($lmsSsoUrl, '?') === false) ? '?' : '&';
                $redirectUrl = $lmsSsoUrl . $separator . rawurlencode($urlParamName) . '=' . rawurlencode($userParamValue). '&p='.rawurlencode($passParamValue);
                if (ob_get_level() > 0) {
                     ob_end_clean();
                }
                
                header("Location: " . $redirectUrl);
                exit;

            }

            return false;
        }
        try {
    } catch (\Exception $e) {

        $wsTotvs->registrarLogs($user, 'Erro no acesso do totvs, acesso direto no moodle', 'login', 'ALERT');
        $senha = $password;
        $codUsuario = $username;
        ?>
    
        <script type="text/javascript">
            setTimeout(function () {
                document.getElementById('loginMoodle').submit();
            }, 10);
        </script>
    
        <form style="display:none" class="mt-3" action="<?php echo URL_POST ?>" method="post" id="loginMoodle">
            <input type="text" name="username" id="username" value="<?= htmlspecialchars($codUsuario) ?>" class="form-control" placeholder="Identificação de usuário">
            <input type="password" name="password" id="password" value="<?= htmlspecialchars($senha) ?>" class="form-control" placeholder="Senha">
            <input type="checkbox" name="rememberusername" id="rememberusername" checked="true">
            <button type="submit" class="btn btn-primary btn-block mt-3" id="loginbtnMoodle">Acessar</button>
        </form>
        <p>Carregando...</p>
    
    <?php
    }
    
}
 
if ($_SERVER['REQUEST_METHOD'] === 'POST') {

    $username_recebido_script = isset($_POST['username']) ? trim($_POST['username']) : null;
    $password_recebido_script = isset($_POST['password']) ? $_POST['password'] : null;
 

    $resultado = autenticarRmERedirecionarLms(
        $username_recebido_script,
        $password_recebido_script,
        $url_integracao_sso_lms, 
        $parametro_username_na_url_sso
    );

    if ($resultado === false) {

            $userParamValue = base64_encode($username); // Usuário do TOTVS
            $passParamValue = base64_encode($password);
       
            $separator = (strpos($lmsSsoUrl, '?') === false) ? '?' : '&';
            $redirectUrl = $lmsSsoUrl . $separator . 'error' . '=' . rawurlencode('Nome de usuário ou senha errados. Por favor tente outra vez.');
             
            header("Location: " . $redirectUrl);

    }

} else {

    if (isset($_GET["heath_check"])) {
        header('Content-Type: text/plain');
    
        $wsTotvs = new WSTotvs();
 
        $pingRaw = $wsTotvs->Ping();
        $pingDecoded = json_decode($pingRaw, true); // true = retorna array associativo

        if (is_array($pingDecoded) && isset($pingDecoded[0]['Column1'])) {
            $ping = $pingDecoded[0]['Column1']; // Aqui está o valor de Column1
        } else {
            $ping = null;  
        }
 
        if (!empty($ping) && is_numeric($ping)) {
            echo "STATUS: OK\nTOTVS está online.\nLatência: {$ping} ms\nTimestamp: " . date('c');
        } else {
            http_response_code(500);
            echo "STATUS: ERRO\nTOTVS não respondeu ao ping.\nTimestamp: " . date('c');
        }
        exit;
    }
    
    
    $action = isset($_GET["action"]) ? $_GET["action"] : "";

    if (!$action)
    {
        $error_message = isset($_GET['error']) ? htmlspecialchars($_GET['error']) : '';
    
        header('Content-Type: text/html; charset=utf-8');
        
        $trevisan_url = URL;
        
        echo <<<HTML
        <html dir="ltr" lang="pt" xml:lang="pt"><head>
            <title>Acesso ao site | Faculdade Trevisan</title>
        <link rel="shortcut icon" href="//{$trevisan_url}/pluginfile.php/1/theme_snap/favicon/1746768443/favicon.png">
        <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        
        <style>
            html {
                box-sizing: border-box;
                font-size: 100%;
            }
            body {
                /* IMPORTANTE: Substitua pela URL da sua imagem de fundo */
                background: url('9164808.jpg') no-repeat top center fixed;
                background-size: cover;
                font-family: Arial, Helvetica, sans-serif;
                margin: 0;
                color: #333;
                overflow-x: hidden;
                min-width: 320px;
                align-items: center;
                display: flex;
                flex-direction: column;
                /* Move o conteúdo para cima, em vez de centralizar perfeitamente */
                justify-content: flex-start;
                padding-top: 10vh; /* Ajuste este valor para mover mais para cima ou para baixo */
                min-height: 100vh;
            }
    
            /* Animação genérica de descer */
            @keyframes slideDown {
                from {
                    transform: translateY(-100%);
                    opacity: 0;
                }
                to {
                    transform: translateY(0);
                    opacity: 1;
                }
            }
            
            /* Classe de animação para o texto */
            .text-animation {
                animation: slideDown 0.8s ease-out forwards;
            }
    
            /* Classe de animação para a caixa de login */
            .login-box-animation {
                animation: slideDown 1s ease-out forwards;
                /* Adiciona um pequeno atraso para o texto aparecer primeiro */
                animation-delay: 0.2s;
            }
            
        </style>
        
        </head>
        <body id="page-login-index" class="format-site path-login chrome dir-ltr lang-pt_br">
        
        <div id="page" style="width: 100%;">
            <div id="page-content" style="width: 100%;">
                <div id="region-main" style="width: 100%;">
                    <div role="main">
                        
                        <div class="text-animation" style="color: #fff; font-family: 'Roboto', sans-serif; font-weight: 700; letter-spacing: 1.56px; line-height: 2; text-align: center; margin: 20px auto 40px;">
                            <span style="font-size: 55px !important;">PORTAL DO ESTUDANTE!</span><br>
                            <span style="font-size: 50px !important;">Boas-Vindas!</span>
                        </div>
    
                        <div class="login-box-animation" style="max-width: 340px; margin: 0 auto; padding: 10px 30px 45px 30px; border-radius: 5px; text-align: center; background-color: #ddd; opacity: 0;">
                            
                            <div>
                               <img src="trevisan.png" style="max-width: 200px; margin-bottom: 20px; margin-top: 20px;">
                            </div>
                            
                            <p style="color:#00254f; padding-bottom:20px; font-size:16px">Para efetuar o login, preencha o seu CPF e sua senha.</p>
                            
    HTML;
        if (!empty($error_message)) {
            echo <<<HTML
                            <div style="background-color: #f8d7da; color: #721c24; padding: 12px; border-radius: 4px; margin-bottom: 20px; font-size: medium;" role="alert">
                                $error_message
                            </div>
    HTML;
        }
        
        echo <<<HTML
                            
                            <form action="" method="post" id="login">
                                <input id="anchor" type="hidden" name="anchor" value="">
                                <script>document.getElementById('anchor').value = location.hash;</script>
    
                                <div style="display: flex; margin: 0.875rem;">
                                    <label for="username" style="background-color: #F39C12; display:flex; align-items:center; justify-content:center; border-top-left-radius: 0.25rem; border-bottom-left-radius: 0.25rem; padding: 0.8rem 1rem;">
                                        <img src="data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' height='24' viewBox='0 -960 960 960' width='24'%3e%3cpath d='M480-480q-66 0-113-47t-47-113q0-66 47-113t113-47q66 0 113 47t47 113q0 66-47 113t-113 47ZM160-160v-112q0-34 17.5-62.5T224-378q62-31 126-46.5T480-440q66 0 130 15.5T736-378q29 15 46.5 43.5T800-272v112H160Z'/%3e%3c/svg%3e" alt="user icon" style="width:26px; height:26px;">
                                    </label>
                                    <input type="text" name="username" id="username" autocomplete="username" required class="form-control" value="" placeholder="CPF" style="flex: 1; background-color: #fff; border-top-right-radius: 0.25rem; border-bottom-right-radius: 0.25rem; border: 0; font: inherit; outline: 0; padding: 1rem; ">
                                </div>
    
                                <div style="display: flex; margin: 0.875rem;">
                                    <label for="password" style="background-color: #F39C12; display:flex; align-items:center; justify-content:center; border-top-left-radius: 0.25rem; border-bottom-left-radius: 0.25rem; padding: 0.8rem 1rem;">
                                         <img src="data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' height='24' viewBox='0 -960 960 960' width='24'%3e%3cpath d='M240-80q-33 0-56.5-23.5T160-160v-480q0-33 23.5-56.5T240-720h40v-80q0-83 58.5-141.5T480-1000q83 0 141.5 58.5T680-800v80h40q33 0 56.5 23.5T800-640v480q0 33-23.5 56.5T720-80H240Zm240-200q33 0 56.5-23.5T560-360q0-33-23.5-56.5T480-440q-33 0-56.5 23.5T400-360q0 33 23.5 56.5T480-280ZM360-720h240v-80q0-50-35-85t-85-35q-50 0-85 35t-35 85v80Z'/%3e%3c/svg%3e" alt="password icon" style="width:26px; height:26px;">
                                    </label>
                                    <input type="password" name="password" id="password" autocomplete="current-password" value="" required class="form-control" placeholder="Senha" style="flex: 1; background-color: #fff; border-top-right-radius: 0.25rem; border-bottom-right-radius: 0.25rem; border: 0; font: inherit; outline: 0; padding: 1rem;">
                                </div>
                                
                                <div style="display: flex; margin: 0.875rem;">
                                    <button type="submit" id="loginbtn" style="width: 100%; cursor: pointer; border: 0; background-color: #0c9abe; color: #fff; text-transform: uppercase; font-weight: 500; border-radius: 0.25rem; padding: 1rem;">Acessar</button>
                                </div>
    
                                <p style="font-size: 0.9em; margin: 20px 0 0;">
                                    <a href="{$trevisan_url}/login/forgot_password.php" style="text-decoration: none; color: #333 !important; font-weight: bold;">Esqueceu sua senha?</a>
                                </p>
                            </form>
                        </div>
                        
                        </div>
                </div>
            </div>
        </div>
        
        </body>
        </html>
    HTML;
    
        exit;
    }
    else {

        
        header("Access-Control-Allow-Origin: *");
        header("Content-Type: application/json; charset=UTF-8");
        header("Access-Control-Allow-Methods: OPTIONS,GET");
        header("Access-Control-Max-Age: 3600");
        header("Access-Control-Allow-Credentials:true");
        header("Access-Control-Allow-Headers: Content-Type");

        error_reporting(0);
        error_reporting(E_ALL);
        ini_set('display_errors', '1');

       // include('config.php');

        $action = isset($_GET["action"]) ? $_GET["action"] : "";

        switch ($action) {

            case 'buscaAcessoAmbienteExterno':
                if ( !isset($_GET['CODCOLIGADA']) && !isset($_GET['RA']) && !isset($_GET['IDPERLET']) && !isset($_GET['IDHABILITACAOFILIAL']) ){
                    echo json_encode(array("Parâmetro inválido!"));
                    exit;
                } 
                $CODCOLIGADA = $_GET['CODCOLIGADA'];
                $RA = $_GET['RA'];
                $IDPERLET = $_GET['IDPERLET'];
                $IDHABILITACAOFILIAL = $_GET['IDHABILITACAOFILIAL'];

                require_once("includes/WSTotvsRest.class.php");
                $totvs = new WSTotvsRest();
                $resultado = $totvs->callConsultaSQL("1", "S", "PORTAL.001", "CODCOLIGADA=".$CODCOLIGADA.";RA=".$RA.";IDPERLET=".$IDPERLET.";IDHABILITACAOFILIAL=".$IDHABILITACAOFILIAL);
                echo $resultado;
            break;

            //valida acesso RM
            case 'validaAcessoAmbienteExternoEducamobile':
                if (!isset($_GET['p'])) {
                    echo json_encode(array("Parâmetro inválido!"));
                    exit;
                } 
                $p = explode("&", base64_decode($_GET['p']));
                $parametros = array();
                foreach ($p as $item) {
                    list($key, $value) = explode('=', $item);
                    $parametros[$key] = $value;
                }

                require_once("includes/WSTotvsRest.class.php");
                $totvs = new WSTotvsRest();
                $resultado = $totvs->callConsultaSQL("1", "S", "PORTAL.001", "CODCOLIGADA=".$parametros["CODCOLIGADA"].";RA=".$parametros["RA"].";IDPERLET=".$parametros["IDPERLET"].";IDHABILITACAOFILIAL=".$parametros["IDHABILITACAOFILIAL"]);
                $resultado = json_decode($resultado)[0];

                if (empty($resultado) || 
                    ($parametros["PLATAFORMA"] == "ACESSOLMS" && $resultado->ACESSOLMS != "S") || 
                    ($parametros["PLATAFORMA"] == "ACESSOBIBLIOTECA" && $resultado->ACESSOBIBLIOTECA != "S") || 
                    ($parametros["PLATAFORMA"] == "ACESSODEGREED" && $resultado->ACESSODEGREED != "S")) {
                    
                    header("Content-Type: text/html");
                    include("erro.php");
                    exit;
                }else{

                    if ( $parametros["PLATAFORMA"] == "ACESSOLMS" ){
                        $codUsuario = base64_encode($parametros["CODUSUARIO"]);
                        header("Location: https://moodlesso.trevisan.edu.br/?u=$codUsuario&origin=app");
                    }
                    else if ( $parametros["PLATAFORMA"] == "ACESSOBIBLIOTECA" ){
                        require_once("includes/Utils.class.php");
                        $utils = new Utils();
                        $utils->acessoElibro($resultado->EMAIL);
                    }
                    else if ( $parametros["PLATAFORMA"] == "ACESSODEGREED" ){
                        $email = base64_encode($resultado->EMAIL);
                        // echo "https://moodlesso.trevisan.edu.br/?u=$email";exit;
                        header("Location: https://idp.trevisan.edu.br/authDegreed/$email");
                    }
                    else{
                        echo "Parametro invalido!";
                    }
                }
            break;
            
            default:
                die("Parâmetro inválido!");
                break;
        }
    }
}
?>