
<?php 

class WSTotvs{
	private $_config;
	private $_dbintegration;
	private $_urltbc;
	private $_usertbc;
	private $_passwordtbc;
	private $_basicParams;
	private $_clientSoap;
    private $_clientSoapWsConsultaSql;
	private $_optionSoap;
    private $_optionSoapLogin;
    private $_optionSoapWsConsultaSql;
    private $_optionSoapWsProcess;

    private $_userTotvs;
    private $_passwordTotvs;
    private $_location;
    private $_locationLogin;
    private $_locationWsConsultaSql;
    private $_locationWsProcess;
    
    private $_alias;

	public function __construct($alias=""){
		include("config.php");

        if ( isset($GLOBALS["alias"]) ){
            switch (@$GLOBALS["alias"]) {
                case 'homolog': $this->_alias = "homolog"; break;
                case 'azurehomolog': $this->_alias = "azurehomolog"; break;
                case 'local': $this->_alias = "local"; break;
                default: $this->_alias = ""; break;
            }
        }else{
            switch ($alias) {
                case 'homolog': $this->_alias = "homolog"; break;
                case 'azurehomolog': $this->_alias = "azurehomolog"; break;
                case 'local': $this->_alias = "local"; break;
                default: $this->_alias = ""; break;
            }
        }
        
        $this->_config = $config["wstotvs".$this->_alias];
        $this->_userTotvs = $this->_config["user"];
        $this->_passwordTotvs = $this->_config["pass"];
    	
        if ( $this->_alias == "azurehomolog" ){
            $this->_basicParams = array('trace'=> 1, 'login' => $this->_userTotvs ,'password' => $this->_passwordTotvs, 'exceptions'=>true, 'cache_wsdl'=>WSDL_CACHE_NONE, "stream_context" => stream_context_create(
                    array(
                        'ssl' => array(
                            'verify_peer'       => false,
                            'verify_peer_name'  => false
                        )
                    )
                ));
        }else{
            $this->_basicParams = array('trace'=> 1, 'login' => $this->_userTotvs ,'password' => $this->_passwordTotvs, 'exceptions'=>true);

        }


        $this->_location = $this->_config["hostTbc"] . 'wsDataServer/IwsDataServer';
        $this->_locationLogin = $this->_config["hostTbc"] . 'wsDataServer/IwsBase';
        $this->_locationWsConsultaSql = $this->_config["hostTbc"] . 'wsConsultaSQL/IwsConsultaSQL';
        $this->_locationWsProcess = $this->_config["hostTbc"] . 'wsProcess/IwsProcess';

        ini_set('soap.wsdl_cache_enabled',0);
        ini_set('soap.wsdl_cache_ttl',0);


    	$this->_clientSoap = new SoapClient($this->_config["hostTbc"] . 'wsDataServer/MEX?WSDL', $this->_basicParams);
        $this->_clientSoapWsConsultaSql = new SoapClient($this->_config["hostTbc"] . 'wsConsultaSQL/MEX?WSDL', $this->_basicParams);
        $this->_clientSoapWsProcess = new SoapClient($this->_config["hostTbc"] . 'wsProcess/MEX?WSDL', $this->_basicParams);

        $this->_optionSoap = array('location' => $this->_location); 
        $this->_optionSoapLogin = array('location' => $this->_locationLogin); 
        $this->_optionSoapWsConsultaSql = array('location' => $this->_locationWsConsultaSql); 
        $this->_optionSoapWsProcess = array('location' => $this->_locationWsProcess); 
       

       /* echo "antes";
        try {
            

            $function = "RealizarConsultaSQL";
            $xmlRecord = array(
                "RealizarConsultaSQL" => array(
                    "codSentenca" => "MEDCEL.0020",
                    "codColigada" => "14",
                    "codSistema" => "G",
                    "parameters" => "NOSSONUMERO=024238134"
                )
            );

            $result = $this->_clientSoapWsConsultaSql->__soapCall($function, $xmlRecord, $this->_optionSoapWsConsultaSql);
            
            print_r($result);exit;

       
        } catch (Exception $e) {
            echo "<h2>Exception Error!</h2>";
            echo $e->getMessage();
        }
        echo "depois";
        exit;*/

	}

    public function buscaVendedor(){
        $function = "RealizarConsultaSQL";
        $xmlRecord = array(
            "RealizarConsultaSQL" => array(
                "codSentenca" => "PS.FICHA.001",
                "codColigada" => "0",
                "codSistema" => "S",
                "parameters" => "CODCOLIGADA=1"
            )
        );

        $result = $this->_clientSoapWsConsultaSql->__soapCall($function, $xmlRecord, $this->_optionSoapWsConsultaSql);

        $xml = simplexml_load_string($result->RealizarConsultaSQLResult);

        return $xml;
    }

    public function buscaCursoMatriculado($cpf){
        $function = "RealizarConsultaSQL";
        $xmlRecord = array(
            "RealizarConsultaSQL" => array(
                "codSentenca" => "PS.FICHA.002",
                "codColigada" => "0",
                "codSistema" => "S",
                "parameters" => "CPF=".$cpf
            )
        );

        $result = $this->_clientSoapWsConsultaSql->__soapCall($function, $xmlRecord, $this->_optionSoapWsConsultaSql);

        $xml = simplexml_load_string($result->RealizarConsultaSQLResult);

        return $xml;
    }



    public function salvaFormContato($dados){

        $xml = "<EduMatricPL>
              <SMatricPL>
                <CODCOLIGADA>".$dados["codColigada"]."</CODCOLIGADA>
                <RA>".$dados["ra"]."</RA>
                <IDHABILITACAOFILIAL>".$dados["idHabilitacaoFilial"]."</IDHABILITACAOFILIAL>
                <IDPERLET>".$dados["idPerlet"]."</IDPERLET>
              </SMatricPL>
              <SMatricPLCompl>
                <CODCOLIGADA>".$dados["codColigada"]."</CODCOLIGADA>
                <RA>".$dados["ra"]."</RA>
                <IDHABILITACAOFILIAL>".$dados["idHabilitacaoFilial"]."</IDHABILITACAOFILIAL>
                <IDPERLET>".$dados["idPerlet"]."</IDPERLET>
                <DATACONTATO>".$dados["dataContato"]."</DATACONTATO>
                <HORACONTATO>".$dados["horaContato"]."</HORACONTATO>
                <FORMACONTATO>".$dados["formaContato"]."</FORMACONTATO>
              </SMatricPLCompl>
        </EduMatricPL>";


            // echo "<xmp>".$xml."</xmp>";exit;

        try {
            $function = "SaveRecord";
            $xml_data = new SimpleXMLElement($xml);
            $xmlRecord = array(
                "SaveRecord" => array(
                    "DataServerName" => "EduMatricPLData",
                    "XML" => $xml_data->asXML(),
                    "Contexto" => "CODCOLIGADA=1"
                )
            );


            $result = $this->_clientSoap->__soapCall($function, $xmlRecord, $this->_optionSoap);
            
            return $result->SaveRecordResult;
        } catch (Exception $e) {
            echo "<h2>Exception Error!</h2>";
            echo $e->getMessage();
        }

    }

    public function salvarTermoAceite($dados){

        $xml = "<PRJ4867328>
              <ZMDACEITEFICHACONSULTOR>
                <ID>1</ID>
                <CODCOLIGADA>".@$dados["CODCOLIGADA"]."</CODCOLIGADA>
                <IDPS>".@$dados["IDPS"]."</IDPS>
                <NUMEROINSCRICAO>".@$dados["NUMEROINSCRICAO"]."</NUMEROINSCRICAO>
                <IDAREAINTERESSE>".@$dados["IDAREAINTERESSE"]."</IDAREAINTERESSE>
                <CODUSUARIOPS>".@$dados["CODUSUARIOPS"]."</CODUSUARIOPS>
                <TEXTOTERMO>".@$dados["TEXTOTERMO"]."</TEXTOTERMO>
                <DATAHORAACEITE>".@$dados["DATAHORAACEITE"]."</DATAHORAACEITE>
                <CLIENTIP>".@$dados["CLIENTIP"]."</CLIENTIP>
              </ZMDACEITEFICHACONSULTOR>
            </PRJ4867328>";

            // echo "<xmp>".$xml."</xmp>";exit;

        try {
            $function = "SaveRecord";
            $xml_data = new SimpleXMLElement($xml);
            $xmlRecord = array(
                "SaveRecord" => array(
                    "DataServerName" => "RMSPRJ4867328Server",
                    "XML" => $xml_data->asXML(),
                    "Contexto" => "CODCOLIGADA=1"
                )
            );


            $result = $this->_clientSoap->__soapCall($function, $xmlRecord, $this->_optionSoap);
            
            return $result->SaveRecordResult;
        } catch (Exception $e) {
            echo "<h2>Exception Error!</h2>";
            echo $e->getMessage();
        }

    }



    /*public function salvaFormContato($dados){

            $curl = curl_init();

            curl_setopt_array($curl, array(
              CURLOPT_URL => $this->_config["hostTbc"] . 'wsDataServer/IwsDataServer',
              CURLOPT_RETURNTRANSFER => true,
              CURLOPT_ENCODING => '',
              CURLOPT_MAXREDIRS => 10,
              CURLOPT_TIMEOUT => 0,
              CURLOPT_FOLLOWLOCATION => true,
              CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
              CURLOPT_CUSTOMREQUEST => 'POST',
              CURLOPT_POSTFIELDS =>'<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:tot="http://www.totvs.com/">
               <soapenv:Header/>
               <soapenv:Body>
                  <tot:SaveRecord>
                     <!--Optional:-->
                     <tot:DataServerName>EduMatricPLData</tot:DataServerName>
                     <!--Optional:-->
                     <tot:XML><![CDATA[<EduMatricPL>
              <SMatricPL>
                <CODCOLIGADA>1</CODCOLIGADA>
                <RA>009408</RA>
                <IDHABILITACAOFILIAL>28</IDHABILITACAOFILIAL>
                <IDPERLET>16</IDPERLET>
              </SMatricPL>
              <SMatricPLCompl>
                <CODCOLIGADA>1</CODCOLIGADA>
                <RA>009408</RA>
                <IDHABILITACAOFILIAL>28</IDHABILITACAOFILIAL>
                <IDPERLET>16</IDPERLET>
                <DATACONTATO>Fri Dec 17 2021 00:00:00 GMT-0300 (Horário Padrão de Brasília)</DATACONTATO>
                <HORACONTATO>1500</HORACONTATO>
                <FORMACONTATO>telefone</FORMACONTATO>
              </SMatricPLCompl>
            </EduMatricPL>]]></tot:XML>
                     <!--Optional:-->
                     <tot:Contexto>codcoligada=1</tot:Contexto>
                  </tot:SaveRecord>
               </soapenv:Body>
            </soapenv:Envelope>',
              CURLOPT_HTTPHEADER => array(
                'SOAPAction: http://www.totvs.com/IwsDataServer/SaveRecord',
                'Accept-Encoding: gzip,deflate',
                'Content-Type: text/xml;charset=UTF-8',
                'Authorization: Basic UFZUOlB2dEAyMDIwIQ=='
              ),
            ));

            $response = curl_exec($curl);

            curl_close($curl);
            return $response;


    }*/


    public function buscaDadosMatricula($codColigada, $idPs, $numeroInscricao){
        $function = "RealizarConsultaSQL";
        $xmlRecord = array(
            "RealizarConsultaSQL" => array(
                "codSentenca" => "PS.DOCXPRESS.001",
                "codColigada" => "0",
                "codSistema" => "S",
                "parameters" => "CODCOLIGADA=".$codColigada.";IDPS=".$idPs.";NUMEROINSCRICAO=".$numeroInscricao
            )
        );

        $result = $this->_clientSoapWsConsultaSql->__soapCall($function, $xmlRecord, $this->_optionSoapWsConsultaSql);
        $xml = simplexml_load_string($result->RealizarConsultaSQLResult);

        return $xml->Resultado;
    }

    public function buscaBannerPS($codColigada, $idPs){
        $function = "RealizarConsultaSQL";
        $xmlRecord = array(
            "RealizarConsultaSQL" => array(
                "codSentenca" => "PS.FICHA.003",
                "codColigada" => "0",
                "codSistema" => "S",
                "parameters" =>"CODCOLIGADA=".$codColigada.";IDPS=".$idPs
            )
        );

        $result = $this->_clientSoapWsConsultaSql->__soapCall($function, $xmlRecord, $this->_optionSoapWsConsultaSql);

        $xml = simplexml_load_string($result->RealizarConsultaSQLResult);

        return $xml;
    }


    public function buscaBannerEdu(){
        $function = "RealizarConsultaSQL";
        $xmlRecord = array(
            "RealizarConsultaSQL" => array(
                "codSentenca" => "PORTAL.LOGIN.001",
                "codColigada" => "0",
                "codSistema" => "S",
                "parameters" => ""
            )
        );

        $result = $this->_clientSoapWsConsultaSql->__soapCall($function, $xmlRecord, $this->_optionSoapWsConsultaSql);

        $xml = simplexml_load_string($result->RealizarConsultaSQLResult);

        return $xml;
    }



}

?>
