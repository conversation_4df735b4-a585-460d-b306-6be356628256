<?php 

class WSTotvsRest{
	private $_config;
  private $_alias;
    
  private $_urlapi;
  private $_port;
  private $_user;
	private $_password;
    

	public function __construct($alias=""){
		include("config.php");

      $this->_alias = @$GLOBALS["alias"];
      $this->_config = $config["wstotvsrest".$this->_alias];

      // print_r($this->_config);exit;

      $this->_urlapi = $this->_config["host"];
      $this->_port = $this->_config["port"];
      $this->_user = $this->_config["user"];
      $this->_password = $this->_config["pass"];
	}

    public function callConsultaSQL($codColigada, $codSistema, $codSentenca, $parametros){
        $curl = curl_init();

        curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, false);
        curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, false);

        curl_setopt_array($curl, array(
          CURLOPT_PORT => $this->_port,
          CURLOPT_URL =>  $this->_urlapi .":".  $this->_port . "/api/framework/v1/consultaSQLServer/RealizaConsulta/".$codSentenca."/".$codColigada."/".$codSistema."?parameters=".$parametros,
          CURLOPT_RETURNTRANSFER => true,
          CURLOPT_ENCODING => "",
          CURLOPT_MAXREDIRS => 10,
          CURLOPT_TIMEOUT => 30,
          CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
          CURLOPT_CUSTOMREQUEST => "GET",
          CURLOPT_HTTPHEADER => array(
            "authorization: Basic ".base64_encode($this->_user.":".$this->_password),
            "cache-control: no-cache"
          ),
        ));
        $response = curl_exec($curl);
        // echo "response: (";print_r($response);echo ")";exit;
        $err = curl_error($curl);

        curl_close($curl);

        if ($err) {
          return "cURL Error #:" . $err;
        } else {
          return $response;
        }

    }


}

?>
