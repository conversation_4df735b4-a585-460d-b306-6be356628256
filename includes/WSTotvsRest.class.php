<?php

class WSTotvsRest
{
  private $_config;
  private $_alias;

  private $_urlapi;
  private $_port;
  private $_user;
  private $_password;


  public function __construct($alias = "")
  {
    include("config.php");

    $this->_alias = @$GLOBALS["alias"];
    $this->_config = $config["wstotvsrest" . $this->_alias];

    // print_r($this->_config);exit;

    $this->_urlapi = $this->_config["host"];
    $this->_port = $this->_config["port"];
    $this->_user = $this->_config["user"];
    $this->_password = $this->_config["pass"];
  }

  public function callConsultaSQL($codColigada, $codSistema, $codSentenca, $parametros)
  {
    // DEBUG: Incluir funções de debug do arquivo principal
    if (!function_exists('startTimer')) {
      include_once(__DIR__ . '/../index.php');
    }

    // DEBUG: Iniciar medição de tempo para consulta SQL REST
    $timerRestSQL = startTimer("TOTVS_REST_SQL_{$codSentenca}");

    $curl = curl_init();

    curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, false);
    curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, false);

    $url = $this->_urlapi . ":" .  $this->_port . "/api/framework/v1/consultaSQLServer/RealizaConsulta/" . $codSentenca . "/" . $codColigada . "/" . $codSistema . "?parameters=" . $parametros;

    curl_setopt_array($curl, array(
      CURLOPT_PORT => $this->_port,
      CURLOPT_URL => $url,
      CURLOPT_RETURNTRANSFER => true,
      CURLOPT_ENCODING => "",
      CURLOPT_MAXREDIRS => 10,
      // OTIMIZAÇÃO: Reduzir timeout total para 20 segundos
      CURLOPT_TIMEOUT => 20,
      // OTIMIZAÇÃO: Timeout de conexão mais agressivo
      CURLOPT_CONNECTTIMEOUT => 10,
      CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
      CURLOPT_CUSTOMREQUEST => "GET",
      // OTIMIZAÇÃO: Habilitar keep-alive para reutilizar conexões
      CURLOPT_TCP_KEEPALIVE => 1,
      CURLOPT_TCP_KEEPIDLE => 30,
      CURLOPT_TCP_KEEPINTVL => 15,
      CURLOPT_HTTPHEADER => array(
        "authorization: Basic " . base64_encode($this->_user . ":" . $this->_password),
        "cache-control: no-cache",
        // OTIMIZAÇÃO: Adicionar Connection keep-alive no header
        "Connection: keep-alive"
      ),
    ));

    // DEBUG: Medição da execução cURL
    $timerCurl = startTimer("CURL_EXEC_REST_SQL");
    $response = curl_exec($curl);
    endTimer($timerCurl, "CURL_EXEC_REST_SQL", "Sentença: {$codSentenca}");

    // echo "response: (";print_r($response);echo ")";exit;
    $err = curl_error($curl);

    curl_close($curl);

    if ($err) {
      endTimer($timerRestSQL, "TOTVS_REST_SQL_{$codSentenca}", "ERRO_CURL: {$err}");
      return "cURL Error #:" . $err;
    } else {
      endTimer($timerRestSQL, "TOTVS_REST_SQL_{$codSentenca}", "SUCESSO - URL: {$url}");
      return $response;
    }
  }
}
