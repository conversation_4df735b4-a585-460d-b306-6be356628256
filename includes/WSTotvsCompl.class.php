
<?php 

class WSTotvsCompl{
	private $_config;
	private $_dbintegration;
	private $_urltbc;
	private $_usertbc;
	private $_passwordtbc;
	private $_basicParams;
	private $_clientSoap;
    private $_clientSoapWsConsultaSql;
	private $_optionSoap;
    private $_optionSoapLogin;
    private $_optionSoapWsConsultaSql;
    private $_optionSoapWsProcess;

    private $_userTotvs;
    private $_passwordTotvs;
    private $_location;
    private $_locationLogin;
    private $_locationWsConsultaSql;
    private $_locationWsProcess;
    
    private $_alias;

	public function __construct($alias=""){
		include("../config.php");

        if ( !isset($_SESSION) ) session_start();
        if ( isset($_SESSION["alias"]) ){
            switch (@$_SESSION["alias"]) {
                case 'homolog': $this->_alias = "homolog"; break;
                case 'azurehomolog': $this->_alias = "azurehomolog"; break;
                default: $this->_alias = ""; break;
            }
        }else{
            switch ($alias) {
                case 'homolog': $this->_alias = "homolog"; break;
                case 'azurehomolog': $this->_alias = "azurehomolog"; break;
                default: $this->_alias = ""; break;
            }
        }
        // print_r($this->_alias);
        
        $this->_config = $config["wstotvs".$this->_alias];
        $this->_userTotvs = $this->_config["user"];
        $this->_passwordTotvs = $this->_config["pass"];
    	
        if ( $this->_alias == "azurehomolog" ){
            $this->_basicParams = array('trace'=> 1, 'login' => $this->_userTotvs ,'password' => $this->_passwordTotvs, 'exceptions'=>true, 'cache_wsdl'=>WSDL_CACHE_NONE, "stream_context" => stream_context_create(
                    array(
                        'ssl' => array(
                            'verify_peer'       => false,
                            'verify_peer_name'  => false
                        )
                    )
                ));
        }else{
            $this->_basicParams = array('trace'=> 1, 'login' => $this->_userTotvs ,'password' => $this->_passwordTotvs, 'exceptions'=>true);

        }


        $this->_location = $this->_config["location"];
        $this->_locationLogin = $this->_config["locationLogin"];
        $this->_locationWsConsultaSql = $this->_config["locationWsConsultaSql"];
        $this->_locationWsProcess = $this->_config["locationWsProcess"];

        ini_set('soap.wsdl_cache_enabled',0);
        ini_set('soap.wsdl_cache_ttl',0);


    	$this->_clientSoap = new SoapClient($this->_config["host"], $this->_basicParams);
        $this->_clientSoapWsConsultaSql = new SoapClient($this->_config["hostWsConsultaSql"], $this->_basicParams);
        $this->_clientSoapWsProcess = new SoapClient($this->_config["hostWsProcess"], $this->_basicParams);

        $this->_optionSoap = array('location' => $this->_location); 
        $this->_optionSoapLogin = array('location' => $this->_locationLogin); 
        $this->_optionSoapWsConsultaSql = array('location' => $this->_locationWsConsultaSql); 
        $this->_optionSoapWsProcess = array('location' => $this->_locationWsProcess); 
       

	}


    public function buscaNomeBase(){
        $function = "RealizarConsultaSQL";
        $xmlRecord = array(
            "RealizarConsultaSQL" => array(
                "codSentenca" => "AGEND.0031",
                "codColigada" => "0",
                "codSistema" => "S",
                "parameters" => ""
            )
        );

        $result = $this->_clientSoapWsConsultaSql->__soapCall($function, $xmlRecord, $this->_optionSoapWsConsultaSql);

        $xml = simplexml_load_string($result->RealizarConsultaSQLResult);

        return $xml;
    }

}

?>
