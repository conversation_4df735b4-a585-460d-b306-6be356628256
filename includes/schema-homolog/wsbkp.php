
<?php 

class WSTotvs{
	private $_config;
	private $_dbintegration;
	private $_urltbc;
	private $_usertbc;
	private $_passwordtbc;
	private $_basicParams;
	private $_clientSoap;
    private $_clientSoapWsConsultaSql;
	private $_optionSoap;
    private $_optionSoapWsConsultaSql;
    private $_optionSoapWsProcess;

    private $_userTotvs;
    private $_passwordTotvs;
    private $_location;
    private $_locationLogin;
    private $_locationWsConsultaSql;
    private $_locationWsProcess;
    
    private $_alias;

	public function __construct($alias=""){
		include("../config.php");

        if ( !isset($_SESSION) ) session_start();
        if ( isset($_SESSION["alias"]) ){
            switch (@$_SESSION["alias"]) {
                case 'homolog': $this->_alias = "homolog"; break;
                case 'azurehomolog': $this->_alias = "azurehomolog"; break;
                default: $this->_alias = ""; break;
            }
        }else{
            switch ($alias) {
                case 'homolog': $this->_alias = "homolog"; break;
                case 'azurehomolog': $this->_alias = "azurehomolog"; break;
                default: $this->_alias = ""; break;
            }
        }
        
        $this->_config = $config["wstotvs".$this->_alias];
        // print_r($this->_config);exit;
        $this->_userTotvs = $this->_config["user"];
        $this->_passwordTotvs = $this->_config["pass"];
    	
        $this->_basicParams = array('trace'=> TRUE, 'login' => $this->_userTotvs ,'password' => $this->_passwordTotvs, 'exceptions'=>true, 'cache_wsdl'=>WSDL_CACHE_NONE);

        $this->_location = $this->_config["location"];
        $this->_locationLogin = $this->_config["locationLogin"];
        $this->_locationWsConsultaSql = $this->_config["locationWsConsultaSql"];
        $this->_locationWsProcess = $this->_config["locationWsProcess"];

        ini_set('soap.wsdl_cache_enabled',0);
        ini_set('soap.wsdl_cache_ttl',0);


        echo "antes";
        try {
            $url = $fullPathToWsdl = dirname(__FILE__) . DIRECTORY_SEPARATOR . "/wsconsultasql.xml";
            // $url = "https://wsprojhmg.afya.com.br";
            // $url = "https://wsprojhmg.afya.com.br/wsConsultaSQL/MEX?wsdl";
            echo $url;
            // $client = new SoapClient($url, [
            //     'login' => 'cleber.vieira',
            //     'password' => '123546',
            //     'stream_context' => stream_context_create([
            //         'ssl' => [
            //             'verify_peer' => false,
            //             'verify_peer_name' => false,
            //         ],
            //     ]),
            // ]);
            // $client = new SoapClient(null, array('location' => $url, 'uri'      => "wsConsultaSQL"));
            // $client = new SoapClient($url, $this->_basicParams);
            // $client->__setLocation('https://wsprojhmg.afya.com.br');
            $client = new SoapClient($url, array(
              // 'location' => 'https://wsprojhmg.afya.com.br/wsConsultaSQL/IwsConsultaSQL',
              /*'user_agent' => 'PHP WS',
              'cache_wsdl' => WSDL_CACHE_NONE,
              'trace' => true,
              'exceptions' => true,
              'features' => SOAP_SINGLE_ELEMENT_ARRAYS,
              'soap_version' => SOAP_1_1,*/
              'login' => $this->_userTotvs ,
              'password' => $this->_passwordTotvs,
              "stream_context" => stream_context_create(
                    array(
                        'ssl' => array(
                            'verify_peer'       => false,
                            'verify_peer_name'  => false
                        )
                    )
                )
            ));
            

            $function = "RealizarConsultaSQL";
            $xmlRecord = array(
                "RealizarConsultaSQL" => array(
                    "codSentenca" => "MEDCEL.0020",
                    "codColigada" => "14",
                    "codSistema" => "G",
                    "parameters" => "NOSSONUMERO=024238134"
                )
            );

            $result = $client->__soapCall($function, $xmlRecord, $this->_optionSoapWsConsultaSql);
            
            print_r($result);exit;

       
        } catch (Exception $e) {
            echo "<h2>Exception Error!</h2>";
            echo $e->getMessage();
        }
        echo "depois";
        exit;













    	$this->_clientSoap = new SoapClient($this->_config["host"], $this->_basicParams);
        $this->_clientSoapWsConsultaSql = new SoapClient($this->_config["hostWsConsultaSql"], $this->_basicParams);
        $this->_clientSoapWsProcess = new SoapClient($this->_config["hostWsProcess"], $this->_basicParams);
    	
        $this->_optionSoap = array('location' => $this->_location); 
        $this->_optionSoapWsConsultaSql = array('location' => $this->_locationWsConsultaSql); 
        $this->_optionSoapWsProcess = array('location' => $this->_locationWsProcess); 

	}


	public function login($usuario, $senha){
        $function = "AutenticaAcesso";
        $xmlRecord = array(
            "AutenticaAcesso" => array()
        );

        $basicParams = array('trace'=> TRUE, 'login' => $usuario ,'password' => $senha);
        $clientSoap = new SoapClient($this->_config["host"], $basicParams);
        $optionSoap = array('location' => $this->_locationLogin); 


        try {
            $result = $clientSoap->__soapCall($function, $xmlRecord, $optionSoap);
            
            return isset($result->AutenticaAcessoResult) ? $result->AutenticaAcessoResult : simplexml_load_string($result);
        } catch (Exception $e) {
            $e->status = "ERROR";
            return $e;           
        }
	}


    public function getAgendamento(){
        $function = "ReadRecord";
        $xmlRecord = array(
            "ReadRecord" => array(
                "DataServerName" => "RMSPRJ5121792Server",
                "PrimaryKey" => "10;2",
                "Contexto" => "CODCOLIGADA=2"
            )
        );


        $result = $this->_clientSoap->__soapCall($function, $xmlRecord, $this->_optionSoap);

        $xml = simplexml_load_string($result->ReadRecordResult);

        print_r($xml);exit;
        return $xml;
    }

    public function deletaAgendamento($ID, $CODCOLIGADA){

        $xml = "<PRJ5121792>
          <ZMDAGENDAMENTO>
            <ID>$ID</ID>
            <CODCOLIGADA>$CODCOLIGADA</CODCOLIGADA>
          </ZMDAGENDAMENTO>
        </PRJ5121792>";
        $function = "DeleteRecord";
        $xml_data = new SimpleXMLElement($xml);
        $xmlRecord = array(
            "DeleteRecord" => array(
                "DataServerName" => "RMSPRJ5121792Server",
                "XML" => $xml_data->asXML(),
                "Contexto" => "CODCOLIGADA=0"
            )
        );


        $result = $this->_clientSoap->__soapCall($function, $xmlRecord, $this->_optionSoap);

        return $result->DeleteRecordResult;
    }

    public function getAgendamentosSalvos(){
        $function = "RealizarConsultaSQL";
        $xmlRecord = array(
            "RealizarConsultaSQL" => array(
                "codSentenca" => "AGEND.0021",
                "codColigada" => "0",
                "codSistema" => "S",
                "parameters" => "CODCOLIGADA=0"
            )
        );

        $result = $this->_clientSoapWsConsultaSql->__soapCall($function, $xmlRecord, $this->_optionSoapWsConsultaSql);

        $xml = simplexml_load_string($result->RealizarConsultaSQLResult);

        return $xml;
    }

    public function buscaAgendamento($ID, $CODCOLIGADA){
        $function = "RealizarConsultaSQL";
        $xmlRecord = array(
            "RealizarConsultaSQL" => array(
                "codSentenca" => "AGEND.0023",
                "codColigada" => "0",
                "codSistema" => "S",
                "parameters" => "CODCOLIGADA=$CODCOLIGADA;ID=$ID"
            )
        );

        $result = $this->_clientSoapWsConsultaSql->__soapCall($function, $xmlRecord, $this->_optionSoapWsConsultaSql);

        $xml = simplexml_load_string($result->RealizarConsultaSQLResult);

        return $xml;
    }

    public function getColigadas(){
        $function = "RealizarConsultaSQL";
        $xmlRecord = array(
            "RealizarConsultaSQL" => array(
                "codSentenca" => "AGEND.0001",
                "codColigada" => "0",
                "codSistema" => "S",
                "parameters" => "CODCOLIGADA=0"
            )
        );

        $result = $this->_clientSoapWsConsultaSql->__soapCall($function, $xmlRecord, $this->_optionSoapWsConsultaSql);

        $xml = simplexml_load_string($result->RealizarConsultaSQLResult);

        return $xml;
    }

    public function getServicos($CODCOLIGADA, $CODFILIAL){
        $function = "RealizarConsultaSQL";
        $xmlRecord = array(
            "RealizarConsultaSQL" => array(
                "codSentenca" => "AGEND.0002",
                "codColigada" => "0",
                "codSistema" => "S",
                "parameters" => "CODCOLIGADA=$CODCOLIGADA;CODFILIAL=$CODFILIAL"
            )
        );

        $result = $this->_clientSoapWsConsultaSql->__soapCall($function, $xmlRecord, $this->_optionSoapWsConsultaSql);

        $xml = simplexml_load_string($result->RealizarConsultaSQLResult);

        return $xml;
    }

    public function getServicosParam($CODCOLIGADA, $CODFILIAL){
        $function = "RealizarConsultaSQL";
        $xmlRecord = array(
            "RealizarConsultaSQL" => array(
                "codSentenca" => "AGEND.0003",
                "codColigada" => "0",
                "codSistema" => "S",
                "parameters" => "CODCOLIGADA=$CODCOLIGADA;CODFILIAL=$CODFILIAL"
            )
        );

        $result = $this->_clientSoapWsConsultaSql->__soapCall($function, $xmlRecord, $this->_optionSoapWsConsultaSql);

        $xml = simplexml_load_string($result->RealizarConsultaSQLResult);

        return $xml;
    }

    public function getTiposDocumento($CODCOLIGADA, $CODFILIAL){
        $function = "RealizarConsultaSQL";
        $xmlRecord = array(
            "RealizarConsultaSQL" => array(
                "codSentenca" => "AGEND.0004",
                "codColigada" => "0",
                "codSistema" => "S",
                "parameters" => "CODCOLIGADA=$CODCOLIGADA;CODFILIAL=$CODFILIAL"
            )
        );

        $result = $this->_clientSoapWsConsultaSql->__soapCall($function, $xmlRecord, $this->_optionSoapWsConsultaSql);

        $xml = simplexml_load_string($result->RealizarConsultaSQLResult);

        return $xml;
    }

    public function getTiposDocumentoParam($CODCOLIGADA, $CODFILIAL){
        $function = "RealizarConsultaSQL";
        $xmlRecord = array(
            "RealizarConsultaSQL" => array(
                "codSentenca" => "AGEND.0005",
                "codColigada" => "0",
                "codSistema" => "S",
                "parameters" => "CODCOLIGADA=$CODCOLIGADA;CODFILIAL=$CODFILIAL"
            )
        );

        $result = $this->_clientSoapWsConsultaSql->__soapCall($function, $xmlRecord, $this->_optionSoapWsConsultaSql);

        $xml = simplexml_load_string($result->RealizarConsultaSQLResult);

        return $xml;
    }

    public function getTipoMovimento($CODCOLIGADA, $CODFILIAL){
        $function = "RealizarConsultaSQL";
        $xmlRecord = array(
            "RealizarConsultaSQL" => array(
                "codSentenca" => "AGEND.0006",
                "codColigada" => "0",
                "codSistema" => "S",
                "parameters" => "CODCOLIGADA=$CODCOLIGADA;CODFILIAL=$CODFILIAL"
            )
        );

        $result = $this->_clientSoapWsConsultaSql->__soapCall($function, $xmlRecord, $this->_optionSoapWsConsultaSql);

        $xml = simplexml_load_string($result->RealizarConsultaSQLResult);

        return $xml;
    }

    public function getTipoMovimentoParam($CODCOLIGADA, $CODFILIAL){
        $function = "RealizarConsultaSQL";
        $xmlRecord = array(
            "RealizarConsultaSQL" => array(
                "codSentenca" => "AGEND.0007",
                "codColigada" => "0",
                "codSistema" => "S",
                "parameters" => "CODCOLIGADA=$CODCOLIGADA;CODFILIAL=$CODFILIAL"
            )
        );

        $result = $this->_clientSoapWsConsultaSql->__soapCall($function, $xmlRecord, $this->_optionSoapWsConsultaSql);

        $xml = simplexml_load_string($result->RealizarConsultaSQLResult);

        return $xml;
    }

    public function getTipoCursoParam($CODCOLIGADA, $CODFILIAL){
        $function = "RealizarConsultaSQL";
        $xmlRecord = array(
            "RealizarConsultaSQL" => array(
                "codSentenca" => "AGEND.0020",
                "codColigada" => "0",
                "codSistema" => "S",
                "parameters" => "CODCOLIGADA=$CODCOLIGADA;CODFILIAL=$CODFILIAL"
            )
        );

        $result = $this->_clientSoapWsConsultaSql->__soapCall($function, $xmlRecord, $this->_optionSoapWsConsultaSql);

        $xml = simplexml_load_string($result->RealizarConsultaSQLResult);

        return $xml;
    }
    
    public function getParamCompetencia($CODCOLIGADA, $CODFILIAL){
        $function = "RealizarConsultaSQL";
        $xmlRecord = array(
            "RealizarConsultaSQL" => array(
                "codSentenca" => "AGEND.0026",
                "codColigada" => "0",
                "codSistema" => "S",
                "parameters" => "CODCOLIGADA=$CODCOLIGADA;CODFILIAL=$CODFILIAL"
            )
        );

        $result = $this->_clientSoapWsConsultaSql->__soapCall($function, $xmlRecord, $this->_optionSoapWsConsultaSql);

        $xml = simplexml_load_string($result->RealizarConsultaSQLResult);

        return $xml;
    }

    public function getProcessoSeletivo($CODCOLIGADA, $CODFILIAL, $DATA_BAIXA_INICIAL, $DATA_BAIXA_FINAL){
        // echo "DTINICIO=$DATA_BAIXA_INICIAL;DTFIM=$DATA_BAIXA_FINAL;CODCOLIGADA=$CODCOLIGADA;CODFILIAL=$CODFILIAL;";exit;
        $function = "RealizarConsultaSQL";
        $xmlRecord = array(
            "RealizarConsultaSQL" => array(
                "codSentenca" => "AGEND.0008",
                "codColigada" => "0",
                "codSistema" => "S",
                "parameters" => "DTINICIO=$DATA_BAIXA_INICIAL;DTFIM=$DATA_BAIXA_FINAL;CODCOLIGADA=$CODCOLIGADA;CODFILIAL=$CODFILIAL"
            )
        );

        $result = $this->_clientSoapWsConsultaSql->__soapCall($function, $xmlRecord, $this->_optionSoapWsConsultaSql);

        $xml = simplexml_load_string($result->RealizarConsultaSQLResult);

        return $xml;
    }

    public function getSituacaoMatricula($CODCOLIGADA, $CODFILIAL){
        $function = "RealizarConsultaSQL";
        $xmlRecord = array(
            "RealizarConsultaSQL" => array(
                "codSentenca" => "AGEND.0009",
                "codColigada" => "0",
                "codSistema" => "S",
                "parameters" => "CODCOLIGADA=$CODCOLIGADA;CODFILIAL=$CODFILIAL"
            )
        );

        $result = $this->_clientSoapWsConsultaSql->__soapCall($function, $xmlRecord, $this->_optionSoapWsConsultaSql);

        $xml = simplexml_load_string($result->RealizarConsultaSQLResult);

        return $xml;
    }


    public function getTipoCurso($CODCOLIGADA, $CODFILIAL){
        $function = "RealizarConsultaSQL";
        $xmlRecord = array(
            "RealizarConsultaSQL" => array(
                "codSentenca" => "AGEND.0019",
                "codColigada" => "0",
                "codSistema" => "S",
                "parameters" => "CODCOLIGADA=$CODCOLIGADA;CODFILIAL=$CODFILIAL"
            )
        );

        $result = $this->_clientSoapWsConsultaSql->__soapCall($function, $xmlRecord, $this->_optionSoapWsConsultaSql);

        $xml = simplexml_load_string($result->RealizarConsultaSQLResult);

        return $xml;
    }


    //busca os tipos de contratosyou
    public function getTipoContrato($CODCOLIGADA, $CODFILIAL){
        $function = "RealizarConsultaSQL";
        $xmlRecord = array(
            "RealizarConsultaSQL" => array(
                "codSentenca" => "AGEND.0027",
                "codColigada" => "0",
                "codSistema" => "S",
                "parameters" => "CODCOLIGADA=$CODCOLIGADA;CODFILIAL=$CODFILIAL"
            )
        );

        $result = $this->_clientSoapWsConsultaSql->__soapCall($function, $xmlRecord, $this->_optionSoapWsConsultaSql);

        $xml = simplexml_load_string($result->RealizarConsultaSQLResult);

        return $xml;
    }


    //busca os tipos de contratos definidos nos parametros
    public function getTipoContratoParam($CODCOLIGADA, $CODFILIAL){
        $function = "RealizarConsultaSQL";
        $xmlRecord = array(
            "RealizarConsultaSQL" => array(
                "codSentenca" => "AGEND.0028",
                "codColigada" => "0",
                "codSistema" => "S",
                "parameters" => "CODCOLIGADA=$CODCOLIGADA;CODFILIAL=$CODFILIAL"
            )
        );

        $result = $this->_clientSoapWsConsultaSql->__soapCall($function, $xmlRecord, $this->_optionSoapWsConsultaSql);

        $xml = simplexml_load_string($result->RealizarConsultaSQLResult);

        return $xml;
    }


    //adiciona parametro
    public function addParam($dataServer, $xml){
        $function = "SaveRecord";
        $xml_data = new SimpleXMLElement($xml);
        $xmlRecord = array(
            "SaveRecord" => array(
                "DataServerName" => $dataServer,
                "XML" => $xml_data->asXML(),
                "Contexto" => "CODCOLIGADA=0"
            )
        );

        $result = $this->_clientSoap->__soapCall($function, $xmlRecord, $this->_optionSoap);

        return $result->SaveRecordResult;
    }


    //remove parametro
    public function removeParam($dataServer, $xml){
        $function = "DeleteRecord";
        $xml_data = new SimpleXMLElement($xml);
        $xmlRecord = array(
            "DeleteRecord" => array(
                "DataServerName" => $dataServer,
                "XML" => $xml_data->asXML(),
                "Contexto" => "CODCOLIGADA=0"
            )
        );

        $result = $this->_clientSoap->__soapCall($function, $xmlRecord, $this->_optionSoap);

        return $result->DeleteRecordResult;
    }

    public function getHistoricoPorStatus(){
        $function = "RealizarConsultaSQL";
        $xmlRecord = array(
            "RealizarConsultaSQL" => array(
                "codSentenca" => "AGEND.0010",
                "codColigada" => "0",
                "codSistema" => "S",
                "parameters" => "CODCOLIGADA=0"
            )
        );

        $result = $this->_clientSoapWsConsultaSql->__soapCall($function, $xmlRecord, $this->_optionSoapWsConsultaSql);

        $xml = simplexml_load_string($result->RealizarConsultaSQLResult);

        return $xml;
    }

    // ========== EMITIR =============
    public function buscaQtdNfeEmitir($CODCOLIGADA, $CODFILIAL, $DATAINICIO, $DATAFIM){
        $function = "RealizarConsultaSQL";
        $xmlRecord = array(
            "RealizarConsultaSQL" => array(
                "codSentenca" => "AGEND.0011",
                "codColigada" => "0",
                "codSistema" => "S",
                "parameters" => "CODCOLIGADA=$CODCOLIGADA;FILIAL=$CODFILIAL;DATAINICIO_D=$DATAINICIO;DATAFIM_D=$DATAFIM"
            )
        );

        $result = $this->_clientSoapWsConsultaSql->__soapCall($function, $xmlRecord, $this->_optionSoapWsConsultaSql);

        $xml = simplexml_load_string($result->RealizarConsultaSQLResult);

        return $xml;
    }

    public function buscaNfeEmitir($CODCOLIGADA, $CODFILIAL, $DATAINICIO, $DATAFIM){
        $function = "RealizarConsultaSQL";
        $xmlRecord = array(
            "RealizarConsultaSQL" => array(
                "codSentenca" => "AGEND.0011.1",
                "codColigada" => "0",
                "codSistema" => "S",
                "parameters" => "CODCOLIGADA=$CODCOLIGADA;FILIAL=$CODFILIAL;DATAINICIO_D=$DATAINICIO;DATAFIM_D=$DATAFIM"
            )
        );

        $result = $this->_clientSoapWsConsultaSql->__soapCall($function, $xmlRecord, $this->_optionSoapWsConsultaSql);

        $xml = simplexml_load_string($result->RealizarConsultaSQLResult);

        return $xml;
    }
    // ========== FIM EMITIR =============

    // ========== EMITIDA =============
    public function buscaQtdNfeEmitida($CODCOLIGADA, $CODFILIAL, $DATAINICIO, $DATAFIM){
        $function = "RealizarConsultaSQL";
        $xmlRecord = array(
            "RealizarConsultaSQL" => array(
                "codSentenca" => "AGEND.0012",
                "codColigada" => "0",
                "codSistema" => "S",
                "parameters" => "CODCOLIGADA=$CODCOLIGADA;FILIAL=$CODFILIAL;DATAINICIO_D=$DATAINICIO;DATAFIM_D=$DATAFIM"
            )
        );

        $result = $this->_clientSoapWsConsultaSql->__soapCall($function, $xmlRecord, $this->_optionSoapWsConsultaSql);

        $xml = simplexml_load_string($result->RealizarConsultaSQLResult);

        return $xml;
    }

    public function buscaNfeEmitida($CODCOLIGADA, $CODFILIAL, $DATAINICIO, $DATAFIM){
        $function = "RealizarConsultaSQL";
        $xmlRecord = array(
            "RealizarConsultaSQL" => array(
                "codSentenca" => "AGEND.0012.1",
                "codColigada" => "0",
                "codSistema" => "S",
                "parameters" => "CODCOLIGADA=$CODCOLIGADA;FILIAL=$CODFILIAL;DATAINICIO_D=$DATAINICIO;DATAFIM_D=$DATAFIM"
            )
        );

        $result = $this->_clientSoapWsConsultaSql->__soapCall($function, $xmlRecord, $this->_optionSoapWsConsultaSql);

        $xml = simplexml_load_string($result->RealizarConsultaSQLResult);

        return $xml;
    }
    // ========== FIM EMITIDA =============

    // ========== INCONSISTENTE =============
    public function buscaQtdNfeInconsistente($CODCOLIGADA, $CODFILIAL, $DATAINICIO, $DATAFIM){
        $function = "RealizarConsultaSQL";
        $xmlRecord = array(
            "RealizarConsultaSQL" => array(
                "codSentenca" => "AGEND.0014",
                "codColigada" => "0",
                "codSistema" => "S",
                "parameters" => "CODCOLIGADA=$CODCOLIGADA;FILIAL=$CODFILIAL;DATAINICIO_D=$DATAINICIO;DATAFIM_D=$DATAFIM"
            )
        );

        $result = $this->_clientSoapWsConsultaSql->__soapCall($function, $xmlRecord, $this->_optionSoapWsConsultaSql);

        $xml = simplexml_load_string($result->RealizarConsultaSQLResult);

        return $xml;
    }

    public function buscaNfeInconsistente($CODCOLIGADA, $CODFILIAL, $DATAINICIO, $DATAFIM){
        $function = "RealizarConsultaSQL";
        $xmlRecord = array(
            "RealizarConsultaSQL" => array(
                "codSentenca" => "AGEND.0014.1",
                "codColigada" => "0",
                "codSistema" => "S",
                "parameters" => "CODCOLIGADA=$CODCOLIGADA;FILIAL=$CODFILIAL;DATAINICIO_D=$DATAINICIO;DATAFIM_D=$DATAFIM"
            )
        );

        $result = $this->_clientSoapWsConsultaSql->__soapCall($function, $xmlRecord, $this->_optionSoapWsConsultaSql);

        $xml = simplexml_load_string($result->RealizarConsultaSQLResult);

        return $xml;
    }
    // ========== FIM INCONSISTENTE =============

    // ========== AGENDAMENTO =============
    public function buscaQtdNfeAgendamento($CODCOLIGADA, $CODFILIAL, $DATAINICIO, $DATAFIM){
        $function = "RealizarConsultaSQL";
        $xmlRecord = array(
            "RealizarConsultaSQL" => array(
                "codSentenca" => "AGEND.0013",
                "codColigada" => "0",
                "codSistema" => "S",
                "parameters" => "CODCOLIGADA=$CODCOLIGADA;FILIAL=$CODFILIAL;DATAINICIO_D=$DATAINICIO;DATAFIM_D=$DATAFIM"
            )
        );

        $result = $this->_clientSoapWsConsultaSql->__soapCall($function, $xmlRecord, $this->_optionSoapWsConsultaSql);

        $xml = simplexml_load_string($result->RealizarConsultaSQLResult);

        return $xml;
    }

    public function buscaNfeAgendamento($CODCOLIGADA, $CODFILIAL, $DATAINICIO, $DATAFIM){
        $function = "RealizarConsultaSQL";
        $xmlRecord = array(
            "RealizarConsultaSQL" => array(
                "codSentenca" => "AGEND.0013.1",
                "codColigada" => "0",
                "codSistema" => "S",
                "parameters" => "CODCOLIGADA=$CODCOLIGADA;FILIAL=$CODFILIAL;DATAINICIO_D=$DATAINICIO;DATAFIM_D=$DATAFIM"
            )
        );

        $result = $this->_clientSoapWsConsultaSql->__soapCall($function, $xmlRecord, $this->_optionSoapWsConsultaSql);

        $xml = simplexml_load_string($result->RealizarConsultaSQLResult);

        return $xml;
    }
    // ========== FIM AGENDAMENTO =============

    // ========== SOLICITACAO GERADA =============
    public function buscaQtdSolicitacaoGerada($CODCOLIGADA, $CODFILIAL, $DATAINICIO, $DATAFIM){
        $function = "RealizarConsultaSQL";
        $xmlRecord = array(
            "RealizarConsultaSQL" => array(
                "codSentenca" => "AGEND.0015",
                "codColigada" => "0",
                "codSistema" => "S",
                "parameters" => "CODCOLIGADA=$CODCOLIGADA;FILIAL=$CODFILIAL;DATAINICIO_D=$DATAINICIO;DATAFIM_D=$DATAFIM"
            )
        );

        $result = $this->_clientSoapWsConsultaSql->__soapCall($function, $xmlRecord, $this->_optionSoapWsConsultaSql);

        $xml = simplexml_load_string($result->RealizarConsultaSQLResult);

        return $xml;
    }

    public function buscaSolicitacaoGerada($CODCOLIGADA, $CODFILIAL, $DATAINICIO, $DATAFIM){
        $function = "RealizarConsultaSQL";
        $xmlRecord = array(
            "RealizarConsultaSQL" => array(
                "codSentenca" => "AGEND.0015.1",
                "codColigada" => "0",
                "codSistema" => "S",
                "parameters" => "CODCOLIGADA=$CODCOLIGADA;FILIAL=$CODFILIAL;DATAINICIO_D=$DATAINICIO;DATAFIM_D=$DATAFIM"
            )
        );

        $result = $this->_clientSoapWsConsultaSql->__soapCall($function, $xmlRecord, $this->_optionSoapWsConsultaSql);

        $xml = simplexml_load_string($result->RealizarConsultaSQLResult);

        return $xml;
    }
    // ========== FIM SOLICITACAO GERADA =============

    // ========== RPS GERADO =============
    public function buscaQtdRPSGerado($CODCOLIGADA, $CODFILIAL, $DATAINICIO, $DATAFIM){
        $function = "RealizarConsultaSQL";
        $xmlRecord = array(
            "RealizarConsultaSQL" => array(
                "codSentenca" => "AGEND.0016",
                "codColigada" => "0",
                "codSistema" => "S",
                "parameters" => "CODCOLIGADA=$CODCOLIGADA;FILIAL=$CODFILIAL;DATAINICIO_D=$DATAINICIO;DATAFIM_D=$DATAFIM"
            )
        );

        $result = $this->_clientSoapWsConsultaSql->__soapCall($function, $xmlRecord, $this->_optionSoapWsConsultaSql);

        $xml = simplexml_load_string($result->RealizarConsultaSQLResult);

        return $xml;
    }

    public function buscaRPSGerado($CODCOLIGADA, $CODFILIAL, $DATAINICIO, $DATAFIM){
        $function = "RealizarConsultaSQL";
        $xmlRecord = array(
            "RealizarConsultaSQL" => array(
                "codSentenca" => "AGEND.0016.1",
                "codColigada" => "0",
                "codSistema" => "S",
                "parameters" => "CODCOLIGADA=$CODCOLIGADA;FILIAL=$CODFILIAL;DATAINICIO_D=$DATAINICIO;DATAFIM_D=$DATAFIM"
            )
        );

        $result = $this->_clientSoapWsConsultaSql->__soapCall($function, $xmlRecord, $this->_optionSoapWsConsultaSql);

        $xml = simplexml_load_string($result->RealizarConsultaSQLResult);

        return $xml;
    }
    // ========== FIM RPS GERADO =============

    // ========== NFE PENDENTE =============
    public function buscaQtdNfePendente($CODCOLIGADA, $CODFILIAL, $DATAINICIO, $DATAFIM){
        $function = "RealizarConsultaSQL";
        $xmlRecord = array(
            "RealizarConsultaSQL" => array(
                "codSentenca" => "AGEND.0017",
                "codColigada" => "0",
                "codSistema" => "S",
                "parameters" => "CODCOLIGADA=$CODCOLIGADA;FILIAL=$CODFILIAL;DATAINICIO_D=$DATAINICIO;DATAFIM_D=$DATAFIM"
            )
        );

        $result = $this->_clientSoapWsConsultaSql->__soapCall($function, $xmlRecord, $this->_optionSoapWsConsultaSql);

        $xml = simplexml_load_string($result->RealizarConsultaSQLResult);

        return $xml;
    }

    public function buscaNfePendente($CODCOLIGADA, $CODFILIAL, $DATAINICIO, $DATAFIM){
        $function = "RealizarConsultaSQL";
        $xmlRecord = array(
            "RealizarConsultaSQL" => array(
                "codSentenca" => "AGEND.0017.1",
                "codColigada" => "0",
                "codSistema" => "S",
                "parameters" => "CODCOLIGADA=$CODCOLIGADA;FILIAL=$CODFILIAL;DATAINICIO_D=$DATAINICIO;DATAFIM_D=$DATAFIM"
            )
        );

        $result = $this->_clientSoapWsConsultaSql->__soapCall($function, $xmlRecord, $this->_optionSoapWsConsultaSql);

        $xml = simplexml_load_string($result->RealizarConsultaSQLResult);

        return $xml;
    }
    // ========== FIM NFE PENDENTE =============

    // ========== NFE AUTORIZADA =============
    public function buscaQtdNfeAutorizada($CODCOLIGADA, $CODFILIAL, $DATAINICIO, $DATAFIM){
        $function = "RealizarConsultaSQL";
        $xmlRecord = array(
            "RealizarConsultaSQL" => array(
                "codSentenca" => "AGEND.0018",
                "codColigada" => "0",
                "codSistema" => "S",
                "parameters" => "CODCOLIGADA=$CODCOLIGADA;FILIAL=$CODFILIAL;DATAINICIO_D=$DATAINICIO;DATAFIM_D=$DATAFIM"
            )
        );

        $result = $this->_clientSoapWsConsultaSql->__soapCall($function, $xmlRecord, $this->_optionSoapWsConsultaSql);

        $xml = simplexml_load_string($result->RealizarConsultaSQLResult);

        return $xml;
    }

    // ========== NFE AUTORIZADA =============
    public function buscaNfeAutorizada($CODCOLIGADA, $CODFILIAL, $DATAINICIO, $DATAFIM){
        $function = "RealizarConsultaSQL";
        $xmlRecord = array(
            "RealizarConsultaSQL" => array(
                "codSentenca" => "AGEND.0018.1",
                "codColigada" => "0",
                "codSistema" => "S",
                "parameters" => "CODCOLIGADA=$CODCOLIGADA;FILIAL=$CODFILIAL;DATAINICIO_D=$DATAINICIO;DATAFIM_D=$DATAFIM"
            )
        );

        $result = $this->_clientSoapWsConsultaSql->__soapCall($function, $xmlRecord, $this->_optionSoapWsConsultaSql);

        $xml = simplexml_load_string($result->RealizarConsultaSQLResult);

        return $xml;
    }
    // ========== FIM NFE AUTORIZADA =============


    // ========== NFE NÃO APTAS =============
    public function buscaNfeNaoApta($CODCOLIGADA, $CODFILIAL, $DATAINICIO, $DATAFIM){
        $function = "RealizarConsultaSQL";
        $xmlRecord = array(
            "RealizarConsultaSQL" => array(
                "codSentenca" => "AGEND.0024",
                "codColigada" => "0",
                "codSistema" => "S",
                "parameters" => "CODCOLIGADA=$CODCOLIGADA;FILIAL=$CODFILIAL;DATAINICIO_D=$DATAINICIO;DATAFIM_D=$DATAFIM"
            )
        );

        $result = $this->_clientSoapWsConsultaSql->__soapCall($function, $xmlRecord, $this->_optionSoapWsConsultaSql);

        $xml = simplexml_load_string($result->RealizarConsultaSQLResult);

        return $xml;
    }
    // ========== FIM NFE NÃO APTAS =============

    // ========== NFE REJEITADAS =============
    public function buscaNfeRejeitada($CODCOLIGADA, $CODFILIAL, $DATAINICIO, $DATAFIM){
        $function = "RealizarConsultaSQL";
        $xmlRecord = array(
            "RealizarConsultaSQL" => array(
                "codSentenca" => "AGEND.0025",
                "codColigada" => "0",
                "codSistema" => "S",
                "parameters" => "CODCOLIGADA=$CODCOLIGADA;FILIAL=$CODFILIAL;DATAINICIO_D=$DATAINICIO;DATAFIM_D=$DATAFIM"
            )
        );

        $result = $this->_clientSoapWsConsultaSql->__soapCall($function, $xmlRecord, $this->_optionSoapWsConsultaSql);

        $xml = simplexml_load_string($result->RealizarConsultaSQLResult);

        return $xml;
    }
    // ========== FIM NFE REJEITADAS =============

    //busca perfil de acesso ao portal
    public function buscaPerfilAcesso($CODUSUARIO){
        $function = "RealizarConsultaSQL";
        $xmlRecord = array(
            "RealizarConsultaSQL" => array(
                "codSentenca" => "AGEND.0022",
                "codColigada" => "0",
                "codSistema" => "S",
                "parameters" => "CODUSUARIO=$CODUSUARIO"
            )
        );

        $result = $this->_clientSoapWsConsultaSql->__soapCall($function, $xmlRecord, $this->_optionSoapWsConsultaSql);
        $xml = simplexml_load_string($result->RealizarConsultaSQLResult);

        return $xml;
    }

	public function saveAgendamento($xml){
        $function = "SaveRecord";
        $xml_data = new SimpleXMLElement($xml);
        $xmlRecord = array(
            "SaveRecord" => array(
                "DataServerName" => "RMSPRJ5121792Server",
                "XML" => $xml_data->asXML(),
                "Contexto" => "CODCOLIGADA=0"
            )
        );

        $result = $this->_clientSoap->__soapCall($function, $xmlRecord, $this->_optionSoap);

        return $result->SaveRecordResult;
	}


    public function cancelaAgendamento($xml){
        $function = "SaveRecord";
        $xml_data = new SimpleXMLElement($xml);
        $xmlRecord = array(
            "SaveRecord" => array(
                "DataServerName" => "RMSPRJ5121792Server",
                "XML" => $xml_data->asXML(),
                "Contexto" => "CODCOLIGADA=0"
            )
        );

        $result = $this->_clientSoap->__soapCall($function, $xmlRecord, $this->_optionSoap);

        return $result->SaveRecordResult;
    }


    public function executaFv($xml){
        $function = "ExecuteWithXmlParams";
        // $xml_data = new SimpleXMLElement($xml);
        $xmlRecord = array(
            "ExecuteWithXmlParams" => array(
                "ProcessServerName" => "GlbWorkflowExecProc",
                "strXmlParams" => $xml
                // "strXmlParams" => $xml_data->asXML()
            )
        );

        $result = $this->_clientSoapWsProcess->__soapCall($function, $xmlRecord, $this->_optionSoapWsProcess);

        return $result->ExecuteWithXmlParamsResult;
    }

    //adiciona parametro
    public function addParametro($xml){
        $function = "SaveRecord";
        $xml_data = new SimpleXMLElement($xml);
        $xmlRecord = array(
            "SaveRecord" => array(
                "DataServerName" => "RMSPRJ5121792Server",
                "XML" => $xml_data->asXML(),
                "Contexto" => "CODCOLIGADA=0"
            )
        );

        $result = $this->_clientSoap->__soapCall($function, $xmlRecord, $this->_optionSoap);

        return $result->SaveRecordResult;
        // print_r($result);
        // $xml = simplexml_load_string($result->SaveRecordResult);

        // return $xml;
    }


     public function toXml( $data, &$xml_data ) {
        foreach( $data as $key => $value ) {
            if( is_numeric($key) ){
                $key = 'item'.$key; //dealing with <0/>..<n/> issues
            }
            if( is_array($value) ) {
                $subnode = $xml_data->addChild($key);
                array_to_xml($value, $subnode);
            } else {
                $xml_data->addChild("$key",htmlspecialchars("$value"));
            }
         }
    }

    public function buscaJobCriado(){
        $function = "RealizarConsultaSQL";
        $xmlRecord = array(
            "RealizarConsultaSQL" => array(
                "codSentenca" => "AGEND.0029",
                "codColigada" => "0",
                "codSistema" => "S",
                "parameters" => ""
            )
        );

        $result = $this->_clientSoapWsConsultaSql->__soapCall($function, $xmlRecord, $this->_optionSoapWsConsultaSql);

        $xml = simplexml_load_string($result->RealizarConsultaSQLResult);

        return $xml;
    }

}

?>
