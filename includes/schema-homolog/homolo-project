<?xml version="1.0" encoding="UTF-8"?>
<con:soapui-project id="9cd44747-2c9f-43c8-bd58-5a6b1600f566" activeEnvironment="Default" name="TEMP AFYA" resourceRoot="" soapui-version="5.5.0" abortOnError="false" runType="SEQUENTIAL" xmlns:con="http://eviware.com/soapui/config"><con:settings/><con:interface xsi:type="con:WsdlInterface" id="232c4f44-0f35-45cb-a039-1d020b53627d" wsaVersion="NONE" name="RM_IwsDataServer" type="wsdl" bindingName="{http://www.totvs.com/}RM_IwsDataServer" soapVersion="1_1" anonymous="optional" definition="http://wstst-nre.totvscloud.com.br:8059/wsDataServer/MEX?wsdl" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"><con:settings/><con:definitionCache/><con:endpoints><con:endpoint>http://*************:8059/wsDataServer/IwsDataServer</con:endpoint></con:endpoints><con:operation id="17a92dbd-6d3b-4154-bd8b-83f9aa6ac079" isOneWay="false" action="http://www.totvs.com/IRMSServer/CheckServiceActivity" name="CheckServiceActivity" bindingOperationName="CheckServiceActivity" type="Request-Response" inputName="" receivesAttachments="false" sendsAttachments="false" anonymous="optional"><con:settings/><con:call id="ba581f5d-e285-4b6f-9ba0-f2f4e083a807" name="Request 1"><con:settings/><con:encoding>UTF-8</con:encoding><con:endpoint>http://*************:8059/wsDataServer/IwsDataServer</con:endpoint><con:request><![CDATA[<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:tot="http://www.totvs.com/">\r
   <soapenv:Header/>\r
   <soapenv:Body>\r
      <tot:CheckServiceActivity/>\r
   </soapenv:Body>\r
</soapenv:Envelope>]]></con:request><con:wsaConfig mustUnderstand="NONE" version="200508" action="http://www.totvs.com/IRMSServer/CheckServiceActivity"/></con:call></con:operation><con:operation id="222a8947-b832-4505-8fa7-97734b8de9ec" isOneWay="false" action="http://www.totvs.com/IwsDataServer/DeleteRecord" name="DeleteRecord" bindingOperationName="DeleteRecord" type="Request-Response" inputName="" receivesAttachments="false" sendsAttachments="false" anonymous="optional"><con:settings/><con:call id="1259e4ac-dad4-4895-8499-d2cbbd129ba1" name="Request 1"><con:settings/><con:encoding>UTF-8</con:encoding><con:endpoint>http://*************:8059/wsDataServer/IwsDataServer</con:endpoint><con:request><![CDATA[<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:tot="http://www.totvs.com/">\r
   <soapenv:Header/>\r
   <soapenv:Body>\r
      <tot:DeleteRecord>\r
         <!--Optional:-->\r
         <tot:DataServerName>?</tot:DataServerName>\r
         <!--Optional:-->\r
         <tot:XML>?</tot:XML>\r
         <!--Optional:-->\r
         <tot:Contexto>?</tot:Contexto>\r
      </tot:DeleteRecord>\r
   </soapenv:Body>\r
</soapenv:Envelope>]]></con:request><con:wsaConfig mustUnderstand="NONE" version="200508" action="http://www.totvs.com/IwsDataServer/DeleteRecord"/></con:call></con:operation><con:operation id="dcb7d7dc-f50b-493d-aafc-b2d2b141a2ec" isOneWay="false" action="http://www.totvs.com/IwsDataServer/DeleteRecordByKey" name="DeleteRecordByKey" bindingOperationName="DeleteRecordByKey" type="Request-Response" inputName="" receivesAttachments="false" sendsAttachments="false" anonymous="optional"><con:settings/><con:call id="791b36b8-c810-4488-8525-bb4de5ebed86" name="Request 1"><con:settings/><con:encoding>UTF-8</con:encoding><con:endpoint>http://*************:8059/wsDataServer/IwsDataServer</con:endpoint><con:request><![CDATA[<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:tot="http://www.totvs.com/">\r
   <soapenv:Header/>\r
   <soapenv:Body>\r
      <tot:DeleteRecordByKey>\r
         <!--Optional:-->\r
         <tot:DataServerName>?</tot:DataServerName>\r
         <!--Optional:-->\r
         <tot:PrimaryKey>?</tot:PrimaryKey>\r
         <!--Optional:-->\r
         <tot:Contexto>?</tot:Contexto>\r
      </tot:DeleteRecordByKey>\r
   </soapenv:Body>\r
</soapenv:Envelope>]]></con:request><con:wsaConfig mustUnderstand="NONE" version="200508" action="http://www.totvs.com/IwsDataServer/DeleteRecordByKey"/></con:call></con:operation><con:operation id="001af5fd-fbd1-4abd-9b28-41c1b3ff9ed6" isOneWay="false" action="http://www.totvs.com/IwsDataServer/DeleteRecordEmail" name="DeleteRecordEmail" bindingOperationName="DeleteRecordEmail" type="Request-Response" inputName="" receivesAttachments="false" sendsAttachments="false" anonymous="optional"><con:settings/><con:call id="0fc7cc73-4273-4242-96c9-c625ca69d8c7" name="Request 1"><con:settings/><con:encoding>UTF-8</con:encoding><con:endpoint>http://*************:8059/wsDataServer/IwsDataServer</con:endpoint><con:request><![CDATA[<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:tot="http://www.totvs.com/">\r
   <soapenv:Header/>\r
   <soapenv:Body>\r
      <tot:DeleteRecordEmail>\r
         <!--Optional:-->\r
         <tot:DataServerName>?</tot:DataServerName>\r
         <!--Optional:-->\r
         <tot:XML>?</tot:XML>\r
         <!--Optional:-->\r
         <tot:Contexto>?</tot:Contexto>\r
         <!--Optional:-->\r
         <tot:EmailUsuarioContexto>?</tot:EmailUsuarioContexto>\r
      </tot:DeleteRecordEmail>\r
   </soapenv:Body>\r
</soapenv:Envelope>]]></con:request><con:wsaConfig mustUnderstand="NONE" version="200508" action="http://www.totvs.com/IwsDataServer/DeleteRecordEmail"/></con:call></con:operation><con:operation id="04d281e1-51b8-499f-b75f-3e0ec928cc15" isOneWay="false" action="http://www.totvs.com/IwsDataServer/GetSchema" name="GetSchema" bindingOperationName="GetSchema" type="Request-Response" inputName="" receivesAttachments="false" sendsAttachments="false" anonymous="optional"><con:settings/><con:call id="9fbc203f-4ac6-46f9-b45b-237ab44fc240" name="Request 1"><con:settings/><con:encoding>UTF-8</con:encoding><con:endpoint>http://*************:8059/wsDataServer/IwsDataServer</con:endpoint><con:request><![CDATA[<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:tot="http://www.totvs.com/">\r
   <soapenv:Header/>\r
   <soapenv:Body>\r
      <tot:GetSchema>\r
         <!--Optional:-->\r
         <tot:DataServerName>?</tot:DataServerName>\r
         <!--Optional:-->\r
         <tot:Contexto>?</tot:Contexto>\r
      </tot:GetSchema>\r
   </soapenv:Body>\r
</soapenv:Envelope>]]></con:request><con:wsaConfig mustUnderstand="NONE" version="200508" action="http://www.totvs.com/IwsDataServer/GetSchema"/></con:call></con:operation><con:operation id="4bbf0b51-f26c-468a-87f0-6716d6f1d2ac" isOneWay="false" action="http://www.totvs.com/IwsDataServer/GetSchemaEmail" name="GetSchemaEmail" bindingOperationName="GetSchemaEmail" type="Request-Response" inputName="" receivesAttachments="false" sendsAttachments="false" anonymous="optional"><con:settings/><con:call id="f6c16cf4-c582-4d47-8ecb-b23b029730c6" name="Request 1"><con:settings/><con:encoding>UTF-8</con:encoding><con:endpoint>http://*************:8059/wsDataServer/IwsDataServer</con:endpoint><con:request><![CDATA[<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:tot="http://www.totvs.com/">\r
   <soapenv:Header/>\r
   <soapenv:Body>\r
      <tot:GetSchemaEmail>\r
         <!--Optional:-->\r
         <tot:DataServerName>?</tot:DataServerName>\r
         <!--Optional:-->\r
         <tot:Contexto>?</tot:Contexto>\r
         <!--Optional:-->\r
         <tot:EmailUsuarioContexto>?</tot:EmailUsuarioContexto>\r
      </tot:GetSchemaEmail>\r
   </soapenv:Body>\r
</soapenv:Envelope>]]></con:request><con:wsaConfig mustUnderstand="NONE" version="200508" action="http://www.totvs.com/IwsDataServer/GetSchemaEmail"/></con:call></con:operation><con:operation id="f0d9b58b-df41-44a8-b528-08eedfd3ed7e" isOneWay="false" action="http://www.totvs.com/IRMSServer/Implements" name="Implements" bindingOperationName="Implements" type="Request-Response" inputName="" receivesAttachments="false" sendsAttachments="false" anonymous="optional"><con:settings/><con:call id="e41f4896-9231-49c2-a17b-436f3a9bcc37" name="Request 1"><con:settings/><con:encoding>UTF-8</con:encoding><con:endpoint>http://*************:8059/wsDataServer/IwsDataServer</con:endpoint><con:request><![CDATA[<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:tot="http://www.totvs.com/">\r
   <soapenv:Header/>\r
   <soapenv:Body>\r
      <tot:Implements>\r
         <!--Optional:-->\r
         <tot:type/>\r
      </tot:Implements>\r
   </soapenv:Body>\r
</soapenv:Envelope>]]></con:request><con:wsaConfig mustUnderstand="NONE" version="200508" action="http://www.totvs.com/IRMSServer/Implements"/></con:call></con:operation><con:operation id="9d824069-190d-49cc-bd20-19f1b544becd" isOneWay="false" action="http://www.totvs.com/IwsDataServer/IsValidDataServer" name="IsValidDataServer" bindingOperationName="IsValidDataServer" type="Request-Response" inputName="" receivesAttachments="false" sendsAttachments="false" anonymous="optional"><con:settings/><con:call id="65c0d132-3759-41a0-bfd0-d642ad20e953" name="Request 1"><con:settings/><con:encoding>UTF-8</con:encoding><con:endpoint>http://*************:8059/wsDataServer/IwsDataServer</con:endpoint><con:request><![CDATA[<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:tot="http://www.totvs.com/">\r
   <soapenv:Header/>\r
   <soapenv:Body>\r
      <tot:IsValidDataServer>\r
         <!--Optional:-->\r
         <tot:DataServerName>?</tot:DataServerName>\r
      </tot:IsValidDataServer>\r
   </soapenv:Body>\r
</soapenv:Envelope>]]></con:request><con:wsaConfig mustUnderstand="NONE" version="200508" action="http://www.totvs.com/IwsDataServer/IsValidDataServer"/></con:call></con:operation><con:operation id="f00ef9ce-2206-4b00-9d77-14f5574734ad" isOneWay="false" action="http://www.totvs.com/IwsDataServer/ReadLookupView" name="ReadLookupView" bindingOperationName="ReadLookupView" type="Request-Response" inputName="" receivesAttachments="false" sendsAttachments="false" anonymous="optional"><con:settings/><con:call id="7db7d50e-b9e2-4087-b204-e446e237af95" name="Request 1"><con:settings/><con:encoding>UTF-8</con:encoding><con:endpoint>http://*************:8059/wsDataServer/IwsDataServer</con:endpoint><con:request><![CDATA[<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:tot="http://www.totvs.com/">\r
   <soapenv:Header/>\r
   <soapenv:Body>\r
      <tot:ReadLookupView>\r
         <!--Optional:-->\r
         <tot:DataServerName>?</tot:DataServerName>\r
         <!--Optional:-->\r
         <tot:Filtro>?</tot:Filtro>\r
         <!--Optional:-->\r
         <tot:Contexto>?</tot:Contexto>\r
         <!--Optional:-->\r
         <tot:OwnerData>?</tot:OwnerData>\r
      </tot:ReadLookupView>\r
   </soapenv:Body>\r
</soapenv:Envelope>]]></con:request><con:wsaConfig mustUnderstand="NONE" version="200508" action="http://www.totvs.com/IwsDataServer/ReadLookupView"/></con:call></con:operation><con:operation id="8a10e2e0-a5e8-42b9-991e-07c8a7913b82" isOneWay="false" action="http://www.totvs.com/IwsDataServer/ReadLookupViewEmail" name="ReadLookupViewEmail" bindingOperationName="ReadLookupViewEmail" type="Request-Response" inputName="" receivesAttachments="false" sendsAttachments="false" anonymous="optional"><con:settings/><con:call id="0462ad54-0bd5-46c6-9674-9aa7bc2f7595" name="Request 1"><con:settings/><con:encoding>UTF-8</con:encoding><con:endpoint>http://*************:8059/wsDataServer/IwsDataServer</con:endpoint><con:request><![CDATA[<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:tot="http://www.totvs.com/">\r
   <soapenv:Header/>\r
   <soapenv:Body>\r
      <tot:ReadLookupViewEmail>\r
         <!--Optional:-->\r
         <tot:DataServerName>?</tot:DataServerName>\r
         <!--Optional:-->\r
         <tot:Filtro>?</tot:Filtro>\r
         <!--Optional:-->\r
         <tot:Contexto>?</tot:Contexto>\r
         <!--Optional:-->\r
         <tot:OwnerData>?</tot:OwnerData>\r
         <!--Optional:-->\r
         <tot:EmailUsuarioContexto>?</tot:EmailUsuarioContexto>\r
      </tot:ReadLookupViewEmail>\r
   </soapenv:Body>\r
</soapenv:Envelope>]]></con:request><con:wsaConfig mustUnderstand="NONE" version="200508" action="http://www.totvs.com/IwsDataServer/ReadLookupViewEmail"/></con:call></con:operation><con:operation id="2b55d263-2ab7-4821-8d49-00a2136c0bb2" isOneWay="false" action="http://www.totvs.com/IwsDataServer/ReadRecord" name="ReadRecord" bindingOperationName="ReadRecord" type="Request-Response" inputName="" receivesAttachments="false" sendsAttachments="false" anonymous="optional"><con:settings/><con:call id="350c4ee5-4775-4c6f-b63a-38eeeacfedc4" name="Request 1"><con:settings/><con:encoding>UTF-8</con:encoding><con:endpoint>http://*************:8059/wsDataServer/IwsDataServer</con:endpoint><con:request><![CDATA[<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:tot="http://www.totvs.com/">\r
   <soapenv:Header/>\r
   <soapenv:Body>\r
      <tot:ReadRecord>\r
         <!--Optional:-->\r
         <tot:DataServerName>?</tot:DataServerName>\r
         <!--Optional:-->\r
         <tot:PrimaryKey>?</tot:PrimaryKey>\r
         <!--Optional:-->\r
         <tot:Contexto>?</tot:Contexto>\r
      </tot:ReadRecord>\r
   </soapenv:Body>\r
</soapenv:Envelope>]]></con:request><con:wsaConfig mustUnderstand="NONE" version="200508" action="http://www.totvs.com/IwsDataServer/ReadRecord"/></con:call><con:call id="1dea3047-8434-4de9-b49a-d75a7c9ab6c2" name="ALUNO"><con:settings><con:setting id="com.eviware.soapui.impl.wsdl.WsdlRequest@request-headers">&lt;xml-fragment/></con:setting></con:settings><con:encoding>UTF-8</con:encoding><con:endpoint>https://wsprojhmg.afya.com.br/wsDataServer/IwsDataServer</con:endpoint><con:request><![CDATA[<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:tot="http://www.totvs.com/">\r
   <soapenv:Header/>\r
   <soapenv:Body>\r
      <tot:SaveRecord>\r
         <!--Optional:-->\r
         <tot:DataServerName>RMSPRJ5121792Server</tot:DataServerName>\r
         <!--Optional:-->\r
         <tot:xml><![CDATA[<PRJ5121792>
  <ZMDAGENDAMENTO>
    <ID>1080</ID>
    <CODCOLIGADA>5</CODCOLIGADA>
    <COTMV>2.1.02</COTMV>
    <CODFILIAL>1</CODFILIAL>
    <DATA_AG_RPS>2020-01-23T00:00:00</DATA_AG_RPS>
    <DATA_AG_ENVIO>2020-01-23T00:00:00</DATA_AG_ENVIO>
    <HORA_AG_RPS>2020-01-23T15:39:00</HORA_AG_RPS>
    <HORA_AG_ENVIO>2020-01-23T15:39:00</HORA_AG_ENVIO>
    <STATUS_EXECUCAO>0</STATUS_EXECUCAO>
    <IDJOB>2171913</IDJOB>
    <EXECUTADO>2</EXECUTADO>
    <PS>S</PS>
    <IDPS>218</IDPS>
    <ATUALIZA_E>N</ATUALIZA_E>
    <RECORRENTE>N</RECORRENTE>
  </ZMDAGENDAMENTO>
</PRJ5121792>]]]]>><![CDATA[</tot:xml>\r
         <!--Optional:-->\r
         <tot:Contexto>CODCOLIGADA=0</tot:Contexto>\r
      </tot:SaveRecord>\r
   </soapenv:Body>\r
</soapenv:Envelope>]]></con:request><con:credentials><con:username>cleber.vieira</con:username><con:password>123456</con:password><con:selectedAuthProfile>Basic</con:selectedAuthProfile><con:addedBasicAuthenticationTypes>Basic</con:addedBasicAuthenticationTypes><con:preemptive>true</con:preemptive><con:authType>Preemptive</con:authType></con:credentials><con:jmsConfig JMSDeliveryMode="PERSISTENT"/><con:jmsPropertyConfig/><con:wsaConfig mustUnderstand="NONE" version="200508" action="http://www.totvs.com/IwsDataServer/ReadRecord"/><con:wsrmConfig version="1.2"/></con:call><con:call id="accaab8a-24cb-46fa-b8a7-30878fe12ff7" name="busca metadados"><con:settings><con:setting id="com.eviware.soapui.impl.wsdl.WsdlRequest@request-headers">&lt;xml-fragment/></con:setting></con:settings><con:encoding>UTF-8</con:encoding><con:endpoint>https://wsprojhmg.afya.com.br/wsDataServer/IwsDataServer</con:endpoint><con:request><![CDATA[<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:tot="http://www.totvs.com/">\r
   <soapenv:Header/>\r
   <soapenv:Body>\r
      <tot:ReadRecord>\r
         <!--Optional:-->\r
         <tot:DataServerName>RMSPRJ5121792Server</tot:DataServerName>\r
         <!--Optional:-->\r
         <tot:PrimaryKey>1080;5</tot:PrimaryKey>\r
         <!--Optional:-->\r
         <tot:Contexto>CODCOLIGADA=0</tot:Contexto>\r
      </tot:ReadRecord>\r
   </soapenv:Body>\r
</soapenv:Envelope>]]></con:request><con:credentials><con:username>cleber.vieira</con:username><con:password>123456</con:password><con:selectedAuthProfile>Basic</con:selectedAuthProfile><con:addedBasicAuthenticationTypes>Basic</con:addedBasicAuthenticationTypes><con:preemptive>true</con:preemptive><con:authType>Preemptive</con:authType></con:credentials><con:jmsConfig JMSDeliveryMode="PERSISTENT"/><con:jmsPropertyConfig/><con:wsaConfig mustUnderstand="NONE" version="200508" action="http://www.totvs.com/IwsDataServer/ReadRecord"/><con:wsrmConfig version="1.2"/></con:call></con:operation><con:operation id="0db7f2c8-e843-4547-b753-87c205079bc8" isOneWay="false" action="http://www.totvs.com/IwsDataServer/ReadRecordEmail" name="ReadRecordEmail" bindingOperationName="ReadRecordEmail" type="Request-Response" inputName="" receivesAttachments="false" sendsAttachments="false" anonymous="optional"><con:settings/><con:call id="272bddfd-cf29-452f-9c52-cfddaddb8b46" name="Request 1"><con:settings/><con:encoding>UTF-8</con:encoding><con:endpoint>http://*************:8059/wsDataServer/IwsDataServer</con:endpoint><con:request><![CDATA[<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:tot="http://www.totvs.com/">\r
   <soapenv:Header/>\r
   <soapenv:Body>\r
      <tot:ReadRecordEmail>\r
         <!--Optional:-->\r
         <tot:DataServerName>?</tot:DataServerName>\r
         <!--Optional:-->\r
         <tot:PrimaryKey>?</tot:PrimaryKey>\r
         <!--Optional:-->\r
         <tot:Contexto>?</tot:Contexto>\r
         <!--Optional:-->\r
         <tot:EmailUsuarioContexto>?</tot:EmailUsuarioContexto>\r
      </tot:ReadRecordEmail>\r
   </soapenv:Body>\r
</soapenv:Envelope>]]></con:request><con:wsaConfig mustUnderstand="NONE" version="200508" action="http://www.totvs.com/IwsDataServer/ReadRecordEmail"/></con:call></con:operation><con:operation id="21d6cf58-9bee-43ac-8301-ed26059d34bf" isOneWay="false" action="http://www.totvs.com/IwsDataServer/ReadView" name="ReadView" bindingOperationName="ReadView" type="Request-Response" inputName="" receivesAttachments="false" sendsAttachments="false" anonymous="optional"><con:settings/><con:call id="78f9abd9-194c-4a73-bee4-9e32c97aa451" name="Request 1"><con:settings/><con:encoding>UTF-8</con:encoding><con:endpoint>http://*************:8059/wsDataServer/IwsDataServer</con:endpoint><con:request><![CDATA[<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:tot="http://www.totvs.com/">\r
   <soapenv:Header/>\r
   <soapenv:Body>\r
      <tot:ReadView>\r
         <!--Optional:-->\r
         <tot:DataServerName>?</tot:DataServerName>\r
         <!--Optional:-->\r
         <tot:Filtro>?</tot:Filtro>\r
         <!--Optional:-->\r
         <tot:Contexto>?</tot:Contexto>\r
      </tot:ReadView>\r
   </soapenv:Body>\r
</soapenv:Envelope>]]></con:request><con:wsaConfig mustUnderstand="NONE" version="200508" action="http://www.totvs.com/IwsDataServer/ReadView"/></con:call></con:operation><con:operation id="0d33aebb-e6d7-4b6d-bfdd-32cfb1d344c0" isOneWay="false" action="http://www.totvs.com/IwsDataServer/ReadViewEmail" name="ReadViewEmail" bindingOperationName="ReadViewEmail" type="Request-Response" inputName="" receivesAttachments="false" sendsAttachments="false" anonymous="optional"><con:settings/><con:call id="2cceb557-c7e5-45f2-b465-823b1f3f4862" name="Request 1"><con:settings/><con:encoding>UTF-8</con:encoding><con:endpoint>http://*************:8059/wsDataServer/IwsDataServer</con:endpoint><con:request><![CDATA[<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:tot="http://www.totvs.com/">\r
   <soapenv:Header/>\r
   <soapenv:Body>\r
      <tot:ReadViewEmail>\r
         <!--Optional:-->\r
         <tot:DataServerName>?</tot:DataServerName>\r
         <!--Optional:-->\r
         <tot:Filtro>?</tot:Filtro>\r
         <!--Optional:-->\r
         <tot:Contexto>?</tot:Contexto>\r
         <!--Optional:-->\r
         <tot:EmailUsuarioContexto>?</tot:EmailUsuarioContexto>\r
      </tot:ReadViewEmail>\r
   </soapenv:Body>\r
</soapenv:Envelope>]]></con:request><con:wsaConfig mustUnderstand="NONE" version="200508" action="http://www.totvs.com/IwsDataServer/ReadViewEmail"/></con:call></con:operation><con:operation id="e9759eb1-7210-4ec6-979a-1ddb954b681e" isOneWay="false" action="http://www.totvs.com/IwsDataServer/SaveRecord" name="SaveRecord" bindingOperationName="SaveRecord" type="Request-Response" inputName="" receivesAttachments="false" sendsAttachments="false" anonymous="optional"><con:settings/><con:call id="e5d214f4-d236-469e-907e-1a62694102f7" name="Request 1"><con:settings/><con:encoding>UTF-8</con:encoding><con:endpoint>http://*************:8059/wsDataServer/IwsDataServer</con:endpoint><con:request><![CDATA[<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:tot="http://www.totvs.com/">\r
   <soapenv:Header/>\r
   <soapenv:Body>\r
      <tot:SaveRecord>\r
         <!--Optional:-->\r
         <tot:DataServerName>?</tot:DataServerName>\r
         <!--Optional:-->\r
         <tot:XML>?</tot:XML>\r
         <!--Optional:-->\r
         <tot:Contexto>?</tot:Contexto>\r
      </tot:SaveRecord>\r
   </soapenv:Body>\r
</soapenv:Envelope>]]></con:request><con:wsaConfig mustUnderstand="NONE" version="200508" action="http://www.totvs.com/IwsDataServer/SaveRecord"/></con:call><con:call id="d540a0ed-80ae-4bc9-ba21-c2dd75f59e2f" name="temp"><con:settings><con:setting id="com.eviware.soapui.impl.wsdl.WsdlRequest@request-headers">&lt;xml-fragment/></con:setting></con:settings><con:encoding>UTF-8</con:encoding><con:endpoint>https://wsprojhmg.afya.com.br/wsDataServer/IwsDataServer</con:endpoint><con:request><![CDATA[<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:tot="http://www.totvs.com/">\r
   <soapenv:Header/>\r
   <soapenv:Body>\r
      <tot:SaveRecord>\r
         <!--Optional:-->\r
         <tot:DataServerName>EduAlunoData</tot:DataServerName>\r
         <!--Optional:-->\r
         <tot:XML>?</tot:XML>\r
         <!--Optional:-->\r
         <tot:Contexto>?</tot:Contexto>\r
      </tot:SaveRecord>\r
   </soapenv:Body>\r
</soapenv:Envelope>]]></con:request><con:credentials><con:authType>No Authorization</con:authType></con:credentials><con:jmsConfig JMSDeliveryMode="PERSISTENT"/><con:jmsPropertyConfig/><con:wsaConfig mustUnderstand="NONE" version="200508" action="http://www.totvs.com/IwsDataServer/SaveRecord"/><con:wsrmConfig version="1.2"/></con:call><con:call id="25c5f4f3-90b6-49f9-a9ce-711a0ed08d3f" name="SALVA METADADOS"><con:settings><con:setting id="com.eviware.soapui.impl.wsdl.WsdlRequest@request-headers">&lt;xml-fragment/></con:setting></con:settings><con:encoding>UTF-8</con:encoding><con:endpoint>https://wsprojhmg.afya.com.br/wsDataServer/IwsDataServer</con:endpoint><con:request><![CDATA[<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:tot="http://www.totvs.com/">\r
   <soapenv:Header/>\r
   <soapenv:Body>\r
      <tot:SaveRecord>\r
         <!--Optional:-->\r
         <tot:DataServerName>RMSPRJ5121792Server</tot:DataServerName>\r
         <!--Optional:-->\r
         <tot:XML><![CDATA[<?xml version="1.0"?>
<PRJ5121792>
          <ZMDAGENDAMENTO>
            <ID>-1</ID>
            <CODCOLIGADA>14</CODCOLIGADA>
            <COTMV>2.1.20</COTMV>
            <DATA_E_INICIAL/>
            <DATA_E_FINAL/>
            <DATA_COMP_INI>01/07/2020</DATA_COMP_INI>
            <DATA_COMP_FIM>31/07/2020</DATA_COMP_FIM>
            <DATA_BAIXA_INI/>
            <DATA_BAIXA_FIM/>
            <PS>N</PS>
            <CODTIPOCURSO/>
            <IDPS/>
            <CODSERVICO/>
            <CODTDO/>
            <CODFILIAL>1</CODFILIAL>
            <DATA_AG_RPS>2020-07-23</DATA_AG_RPS>
            <DATA_AG_ENVIO>2020-07-23</DATA_AG_ENVIO>
            <DATA_AG_CONSULTA/>
            <HORA_AG_RPS>23:59</HORA_AG_RPS>
            <HORA_AG_ENVIO>23:59</HORA_AG_ENVIO>
            <HORA_AG_CONSULTA/>
            <STATUS_EXECUCAO>0</STATUS_EXECUCAO>
            <ATUALIZA_E>N</ATUALIZA_E>
            <RECORRENTE>N</RECORRENTE>
            <ENVIONFSE>N</ENVIONFSE>
            <RECCREATEDBY>10</RECCREATEDBY>
            <CODTCN>001</CODTCN>
            <IDWORKFLOW>606</IDWORKFLOW>
          </ZMDAGENDAMENTO>
        </PRJ5121792>
]]]]>><![CDATA[</tot:XML>\r
         <!--Optional:-->\r
         <tot:Contexto>CODCOLIGADA=0</tot:Contexto>\r
      </tot:SaveRecord>\r
   </soapenv:Body>\r
</soapenv:Envelope>]]></con:request><con:credentials><con:username>cleber.vieira</con:username><con:password>123456</con:password><con:selectedAuthProfile>Basic</con:selectedAuthProfile><con:addedBasicAuthenticationTypes>Basic</con:addedBasicAuthenticationTypes><con:preemptive>true</con:preemptive><con:authType>Preemptive</con:authType></con:credentials><con:jmsConfig JMSDeliveryMode="PERSISTENT"/><con:jmsPropertyConfig/><con:wsaConfig mustUnderstand="NONE" version="200508" action="http://www.totvs.com/IwsDataServer/SaveRecord"/><con:wsrmConfig version="1.2"/></con:call></con:operation><con:operation id="9e4aed2b-0cff-4a72-adde-ce4edbbbb158" isOneWay="false" action="http://www.totvs.com/IwsDataServer/SaveRecordEmail" name="SaveRecordEmail" bindingOperationName="SaveRecordEmail" type="Request-Response" inputName="" receivesAttachments="false" sendsAttachments="false" anonymous="optional"><con:settings/><con:call id="cb87de39-d223-4a69-974e-738cf9293ee3" name="Request 1"><con:settings/><con:encoding>UTF-8</con:encoding><con:endpoint>http://*************:8059/wsDataServer/IwsDataServer</con:endpoint><con:request><![CDATA[<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:tot="http://www.totvs.com/">\r
   <soapenv:Header/>\r
   <soapenv:Body>\r
      <tot:SaveRecordEmail>\r
         <!--Optional:-->\r
         <tot:DataServerName>?</tot:DataServerName>\r
         <!--Optional:-->\r
         <tot:XML>?</tot:XML>\r
         <!--Optional:-->\r
         <tot:Contexto>?</tot:Contexto>\r
         <!--Optional:-->\r
         <tot:EmailUsuarioContexto>?</tot:EmailUsuarioContexto>\r
      </tot:SaveRecordEmail>\r
   </soapenv:Body>\r
</soapenv:Envelope>]]></con:request><con:wsaConfig mustUnderstand="NONE" version="200508" action="http://www.totvs.com/IwsDataServer/SaveRecordEmail"/></con:call></con:operation></con:interface><con:interface xsi:type="con:WsdlInterface" id="400043d4-93b6-4c58-a920-f9302d9c78fa" wsaVersion="NONE" name="RM_IwsBase" type="wsdl" bindingName="{http://www.totvs.com/}RM_IwsBase" soapVersion="1_1" anonymous="optional" definition="http://wstst-nre.totvscloud.com.br:8059/wsDataServer/MEX?wsdl" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"><con:settings/><con:definitionCache/><con:endpoints><con:endpoint>http://*************:8059/wsDataServer/IwsBase</con:endpoint></con:endpoints><con:operation id="751d4526-b437-48f9-9e46-cabb69289f07" isOneWay="false" action="http://www.totvs.com/IwsBase/AutenticaAcesso" name="AutenticaAcesso" bindingOperationName="AutenticaAcesso" type="Request-Response" inputName="" receivesAttachments="false" sendsAttachments="false" anonymous="optional"><con:settings/><con:call id="c76e4937-01bc-48c5-8b86-b0b60bcd9e73" name="Request 1"><con:settings/><con:encoding>UTF-8</con:encoding><con:endpoint>http://*************:8059/wsDataServer/IwsBase</con:endpoint><con:request><![CDATA[<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:tot="http://www.totvs.com/">\r
   <soapenv:Header/>\r
   <soapenv:Body>\r
      <tot:AutenticaAcesso/>\r
   </soapenv:Body>\r
</soapenv:Envelope>]]></con:request><con:wsaConfig mustUnderstand="NONE" version="200508" action="http://www.totvs.com/IwsBase/AutenticaAcesso"/></con:call></con:operation><con:operation id="8c34435a-f7b3-4ac0-9bd1-c911398bf307" isOneWay="false" action="http://www.totvs.com/IRMSServer/CheckServiceActivity" name="CheckServiceActivity" bindingOperationName="CheckServiceActivity" type="Request-Response" inputName="" receivesAttachments="false" sendsAttachments="false" anonymous="optional"><con:settings/><con:call id="4c63051e-7cec-4a7c-956d-1f10bc85a220" name="Request 1"><con:settings/><con:encoding>UTF-8</con:encoding><con:endpoint>http://*************:8059/wsDataServer/IwsBase</con:endpoint><con:request><![CDATA[<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:tot="http://www.totvs.com/">\r
   <soapenv:Header/>\r
   <soapenv:Body>\r
      <tot:CheckServiceActivity/>\r
   </soapenv:Body>\r
</soapenv:Envelope>]]></con:request><con:wsaConfig mustUnderstand="NONE" version="200508" action="http://www.totvs.com/IRMSServer/CheckServiceActivity"/></con:call></con:operation><con:operation id="08c3493c-5aa9-4be0-983f-fb743dfd2315" isOneWay="false" action="http://www.totvs.com/IRMSServer/Implements" name="Implements" bindingOperationName="Implements" type="Request-Response" inputName="" receivesAttachments="false" sendsAttachments="false" anonymous="optional"><con:settings/><con:call id="1aaa69d1-c4eb-4e0b-bfbe-028ad9bfe3b7" name="Request 1"><con:settings/><con:encoding>UTF-8</con:encoding><con:endpoint>http://*************:8059/wsDataServer/IwsBase</con:endpoint><con:request><![CDATA[<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:tot="http://www.totvs.com/">\r
   <soapenv:Header/>\r
   <soapenv:Body>\r
      <tot:Implements>\r
         <!--Optional:-->\r
         <tot:type/>\r
      </tot:Implements>\r
   </soapenv:Body>\r
</soapenv:Envelope>]]></con:request><con:wsaConfig mustUnderstand="NONE" version="200508" action="http://www.totvs.com/IRMSServer/Implements"/></con:call></con:operation></con:interface><con:interface xsi:type="con:WsdlInterface" id="e4674d5c-f359-4d6b-afdd-25349d800b81" wsaVersion="NONE" name="RM_IRMSServer" type="wsdl" bindingName="{http://www.totvs.com/}RM_IRMSServer" soapVersion="1_1" anonymous="optional" definition="http://wstst-nre.totvscloud.com.br:8059/wsDataServer/MEX?wsdl" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"><con:settings/><con:definitionCache type="TEXT" rootPart="http://wstst-nre.totvscloud.com.br:8059/wsDataServer/MEX?wsdl"><con:part><con:url>http://wstst-nre.totvscloud.com.br:8059/wsDataServer/MEX?wsdl</con:url><con:content><![CDATA[<wsdl:definitions name="wsDataServer" targetNamespace="http://www.totvs.com/" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/" xmlns:wsx="http://schemas.xmlsoap.org/ws/2004/09/mex" xmlns:wsu="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-utility-1.0.xsd" xmlns:wsa10="http://www.w3.org/2005/08/addressing" xmlns:wsp="http://www.w3.org/ns/ws-policy" xmlns:wsap="http://schemas.xmlsoap.org/ws/2004/08/addressing/policy" xmlns:msc="http://schemas.microsoft.com/ws/2005/12/wsdl/contract" xmlns:soap12="http://schemas.xmlsoap.org/wsdl/soap12/" xmlns:wsa="http://schemas.xmlsoap.org/ws/2004/08/addressing" xmlns:wsam="http://www.w3.org/2007/05/addressing/metadata" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:tns="http://www.totvs.com/" xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:wsaw="http://www.w3.org/2006/05/addressing/wsdl" xmlns:soapenc="http://schemas.xmlsoap.org/soap/encoding/">
  <wsdl:types>
    <xsd:schema targetNamespace="http://www.totvs.com/Imports">
      <xsd:import schemaLocation="http://*************:8059/wsDataServer/mex?xsd=xsd0" namespace="http://www.totvs.com/"/>
      <xsd:import schemaLocation="http://*************:8059/wsDataServer/mex?xsd=xsd1" namespace="http://schemas.microsoft.com/2003/10/Serialization/"/>
      <xsd:import schemaLocation="http://*************:8059/wsDataServer/mex?xsd=xsd2" namespace="http://schemas.datacontract.org/2004/07/System"/>
      <xsd:import schemaLocation="http://*************:8059/wsDataServer/mex?xsd=xsd3" namespace="http://schemas.datacontract.org/2004/07/System.Reflection"/>
    </xsd:schema>
  </wsdl:types>
  <wsdl:message name="IRMSServer_Implements_InputMessage">
    <wsdl:part name="parameters" element="tns:Implements"/>
  </wsdl:message>
  <wsdl:message name="IRMSServer_Implements_OutputMessage">
    <wsdl:part name="parameters" element="tns:ImplementsResponse"/>
  </wsdl:message>
  <wsdl:message name="IRMSServer_CheckServiceActivity_InputMessage">
    <wsdl:part name="parameters" element="tns:CheckServiceActivity"/>
  </wsdl:message>
  <wsdl:message name="IRMSServer_CheckServiceActivity_OutputMessage">
    <wsdl:part name="parameters" element="tns:CheckServiceActivityResponse"/>
  </wsdl:message>
  <wsdl:message name="IwsBase_AutenticaAcesso_InputMessage">
    <wsdl:part name="parameters" element="tns:AutenticaAcesso"/>
  </wsdl:message>
  <wsdl:message name="IwsBase_AutenticaAcesso_OutputMessage">
    <wsdl:part name="parameters" element="tns:AutenticaAcessoResponse"/>
  </wsdl:message>
  <wsdl:message name="IwsDataServer_GetSchema_InputMessage">
    <wsdl:part name="parameters" element="tns:GetSchema"/>
  </wsdl:message>
  <wsdl:message name="IwsDataServer_GetSchema_OutputMessage">
    <wsdl:part name="parameters" element="tns:GetSchemaResponse"/>
  </wsdl:message>
  <wsdl:message name="IwsDataServer_IsValidDataServer_InputMessage">
    <wsdl:part name="parameters" element="tns:IsValidDataServer"/>
  </wsdl:message>
  <wsdl:message name="IwsDataServer_IsValidDataServer_OutputMessage">
    <wsdl:part name="parameters" element="tns:IsValidDataServerResponse"/>
  </wsdl:message>
  <wsdl:message name="IwsDataServer_GetSchemaEmail_InputMessage">
    <wsdl:part name="parameters" element="tns:GetSchemaEmail"/>
  </wsdl:message>
  <wsdl:message name="IwsDataServer_GetSchemaEmail_OutputMessage">
    <wsdl:part name="parameters" element="tns:GetSchemaEmailResponse"/>
  </wsdl:message>
  <wsdl:message name="IwsDataServer_ReadView_InputMessage">
    <wsdl:part name="parameters" element="tns:ReadView"/>
  </wsdl:message>
  <wsdl:message name="IwsDataServer_ReadView_OutputMessage">
    <wsdl:part name="parameters" element="tns:ReadViewResponse"/>
  </wsdl:message>
  <wsdl:message name="IwsDataServer_ReadViewEmail_InputMessage">
    <wsdl:part name="parameters" element="tns:ReadViewEmail"/>
  </wsdl:message>
  <wsdl:message name="IwsDataServer_ReadViewEmail_OutputMessage">
    <wsdl:part name="parameters" element="tns:ReadViewEmailResponse"/>
  </wsdl:message>
  <wsdl:message name="IwsDataServer_ReadRecord_InputMessage">
    <wsdl:part name="parameters" element="tns:ReadRecord"/>
  </wsdl:message>
  <wsdl:message name="IwsDataServer_ReadRecord_OutputMessage">
    <wsdl:part name="parameters" element="tns:ReadRecordResponse"/>
  </wsdl:message>
  <wsdl:message name="IwsDataServer_ReadRecordEmail_InputMessage">
    <wsdl:part name="parameters" element="tns:ReadRecordEmail"/>
  </wsdl:message>
  <wsdl:message name="IwsDataServer_ReadRecordEmail_OutputMessage">
    <wsdl:part name="parameters" element="tns:ReadRecordEmailResponse"/>
  </wsdl:message>
  <wsdl:message name="IwsDataServer_SaveRecord_InputMessage">
    <wsdl:part name="parameters" element="tns:SaveRecord"/>
  </wsdl:message>
  <wsdl:message name="IwsDataServer_SaveRecord_OutputMessage">
    <wsdl:part name="parameters" element="tns:SaveRecordResponse"/>
  </wsdl:message>
  <wsdl:message name="IwsDataServer_SaveRecordEmail_InputMessage">
    <wsdl:part name="parameters" element="tns:SaveRecordEmail"/>
  </wsdl:message>
  <wsdl:message name="IwsDataServer_SaveRecordEmail_OutputMessage">
    <wsdl:part name="parameters" element="tns:SaveRecordEmailResponse"/>
  </wsdl:message>
  <wsdl:message name="IwsDataServer_DeleteRecord_InputMessage">
    <wsdl:part name="parameters" element="tns:DeleteRecord"/>
  </wsdl:message>
  <wsdl:message name="IwsDataServer_DeleteRecord_OutputMessage">
    <wsdl:part name="parameters" element="tns:DeleteRecordResponse"/>
  </wsdl:message>
  <wsdl:message name="IwsDataServer_DeleteRecordEmail_InputMessage">
    <wsdl:part name="parameters" element="tns:DeleteRecordEmail"/>
  </wsdl:message>
  <wsdl:message name="IwsDataServer_DeleteRecordEmail_OutputMessage">
    <wsdl:part name="parameters" element="tns:DeleteRecordEmailResponse"/>
  </wsdl:message>
  <wsdl:message name="IwsDataServer_DeleteRecordByKey_InputMessage">
    <wsdl:part name="parameters" element="tns:DeleteRecordByKey"/>
  </wsdl:message>
  <wsdl:message name="IwsDataServer_DeleteRecordByKey_OutputMessage">
    <wsdl:part name="parameters" element="tns:DeleteRecordByKeyResponse"/>
  </wsdl:message>
  <wsdl:message name="IwsDataServer_ReadLookupView_InputMessage">
    <wsdl:part name="parameters" element="tns:ReadLookupView"/>
  </wsdl:message>
  <wsdl:message name="IwsDataServer_ReadLookupView_OutputMessage">
    <wsdl:part name="parameters" element="tns:ReadLookupViewResponse"/>
  </wsdl:message>
  <wsdl:message name="IwsDataServer_ReadLookupViewEmail_InputMessage">
    <wsdl:part name="parameters" element="tns:ReadLookupViewEmail"/>
  </wsdl:message>
  <wsdl:message name="IwsDataServer_ReadLookupViewEmail_OutputMessage">
    <wsdl:part name="parameters" element="tns:ReadLookupViewEmailResponse"/>
  </wsdl:message>
  <wsdl:portType name="IRMSServer">
    <wsdl:operation name="Implements">
      <wsdl:input wsam:Action="http://www.totvs.com/IRMSServer/Implements" message="tns:IRMSServer_Implements_InputMessage"/>
      <wsdl:output wsam:Action="http://www.totvs.com/IRMSServer/ImplementsResponse" message="tns:IRMSServer_Implements_OutputMessage"/>
    </wsdl:operation>
    <wsdl:operation name="CheckServiceActivity">
      <wsdl:input wsam:Action="http://www.totvs.com/IRMSServer/CheckServiceActivity" message="tns:IRMSServer_CheckServiceActivity_InputMessage"/>
      <wsdl:output wsam:Action="http://www.totvs.com/IRMSServer/CheckServiceActivityResponse" message="tns:IRMSServer_CheckServiceActivity_OutputMessage"/>
    </wsdl:operation>
  </wsdl:portType>
  <wsdl:portType name="IwsBase">
    <wsdl:operation name="Implements">
      <wsdl:input wsam:Action="http://www.totvs.com/IRMSServer/Implements" message="tns:IRMSServer_Implements_InputMessage"/>
      <wsdl:output wsam:Action="http://www.totvs.com/IRMSServer/ImplementsResponse" message="tns:IRMSServer_Implements_OutputMessage"/>
    </wsdl:operation>
    <wsdl:operation name="CheckServiceActivity">
      <wsdl:input wsam:Action="http://www.totvs.com/IRMSServer/CheckServiceActivity" message="tns:IRMSServer_CheckServiceActivity_InputMessage"/>
      <wsdl:output wsam:Action="http://www.totvs.com/IRMSServer/CheckServiceActivityResponse" message="tns:IRMSServer_CheckServiceActivity_OutputMessage"/>
    </wsdl:operation>
    <wsdl:operation name="AutenticaAcesso">
      <wsdl:input wsam:Action="http://www.totvs.com/IwsBase/AutenticaAcesso" message="tns:IwsBase_AutenticaAcesso_InputMessage"/>
      <wsdl:output wsam:Action="http://www.totvs.com/IwsBase/AutenticaAcessoResponse" message="tns:IwsBase_AutenticaAcesso_OutputMessage"/>
    </wsdl:operation>
  </wsdl:portType>
  <wsdl:portType name="IwsDataServer">
    <wsdl:operation name="Implements">
      <wsdl:input wsam:Action="http://www.totvs.com/IRMSServer/Implements" message="tns:IRMSServer_Implements_InputMessage"/>
      <wsdl:output wsam:Action="http://www.totvs.com/IRMSServer/ImplementsResponse" message="tns:IRMSServer_Implements_OutputMessage"/>
    </wsdl:operation>
    <wsdl:operation name="CheckServiceActivity">
      <wsdl:input wsam:Action="http://www.totvs.com/IRMSServer/CheckServiceActivity" message="tns:IRMSServer_CheckServiceActivity_InputMessage"/>
      <wsdl:output wsam:Action="http://www.totvs.com/IRMSServer/CheckServiceActivityResponse" message="tns:IRMSServer_CheckServiceActivity_OutputMessage"/>
    </wsdl:operation>
    <wsdl:operation name="GetSchema">
      <wsdl:input wsam:Action="http://www.totvs.com/IwsDataServer/GetSchema" message="tns:IwsDataServer_GetSchema_InputMessage"/>
      <wsdl:output wsam:Action="http://www.totvs.com/IwsDataServer/GetSchemaResponse" message="tns:IwsDataServer_GetSchema_OutputMessage"/>
    </wsdl:operation>
    <wsdl:operation name="IsValidDataServer">
      <wsdl:input wsam:Action="http://www.totvs.com/IwsDataServer/IsValidDataServer" message="tns:IwsDataServer_IsValidDataServer_InputMessage"/>
      <wsdl:output wsam:Action="http://www.totvs.com/IwsDataServer/IsValidDataServerResponse" message="tns:IwsDataServer_IsValidDataServer_OutputMessage"/>
    </wsdl:operation>
    <wsdl:operation name="GetSchemaEmail">
      <wsdl:input wsam:Action="http://www.totvs.com/IwsDataServer/GetSchemaEmail" message="tns:IwsDataServer_GetSchemaEmail_InputMessage"/>
      <wsdl:output wsam:Action="http://www.totvs.com/IwsDataServer/GetSchemaEmailResponse" message="tns:IwsDataServer_GetSchemaEmail_OutputMessage"/>
    </wsdl:operation>
    <wsdl:operation name="ReadView">
      <wsdl:input wsam:Action="http://www.totvs.com/IwsDataServer/ReadView" message="tns:IwsDataServer_ReadView_InputMessage"/>
      <wsdl:output wsam:Action="http://www.totvs.com/IwsDataServer/ReadViewResponse" message="tns:IwsDataServer_ReadView_OutputMessage"/>
    </wsdl:operation>
    <wsdl:operation name="ReadViewEmail">
      <wsdl:input wsam:Action="http://www.totvs.com/IwsDataServer/ReadViewEmail" message="tns:IwsDataServer_ReadViewEmail_InputMessage"/>
      <wsdl:output wsam:Action="http://www.totvs.com/IwsDataServer/ReadViewEmailResponse" message="tns:IwsDataServer_ReadViewEmail_OutputMessage"/>
    </wsdl:operation>
    <wsdl:operation name="ReadRecord">
      <wsdl:input wsam:Action="http://www.totvs.com/IwsDataServer/ReadRecord" message="tns:IwsDataServer_ReadRecord_InputMessage"/>
      <wsdl:output wsam:Action="http://www.totvs.com/IwsDataServer/ReadRecordResponse" message="tns:IwsDataServer_ReadRecord_OutputMessage"/>
    </wsdl:operation>
    <wsdl:operation name="ReadRecordEmail">
      <wsdl:input wsam:Action="http://www.totvs.com/IwsDataServer/ReadRecordEmail" message="tns:IwsDataServer_ReadRecordEmail_InputMessage"/>
      <wsdl:output wsam:Action="http://www.totvs.com/IwsDataServer/ReadRecordEmailResponse" message="tns:IwsDataServer_ReadRecordEmail_OutputMessage"/>
    </wsdl:operation>
    <wsdl:operation name="SaveRecord">
      <wsdl:input wsam:Action="http://www.totvs.com/IwsDataServer/SaveRecord" message="tns:IwsDataServer_SaveRecord_InputMessage"/>
      <wsdl:output wsam:Action="http://www.totvs.com/IwsDataServer/SaveRecordResponse" message="tns:IwsDataServer_SaveRecord_OutputMessage"/>
    </wsdl:operation>
    <wsdl:operation name="SaveRecordEmail">
      <wsdl:input wsam:Action="http://www.totvs.com/IwsDataServer/SaveRecordEmail" message="tns:IwsDataServer_SaveRecordEmail_InputMessage"/>
      <wsdl:output wsam:Action="http://www.totvs.com/IwsDataServer/SaveRecordEmailResponse" message="tns:IwsDataServer_SaveRecordEmail_OutputMessage"/>
    </wsdl:operation>
    <wsdl:operation name="DeleteRecord">
      <wsdl:input wsam:Action="http://www.totvs.com/IwsDataServer/DeleteRecord" message="tns:IwsDataServer_DeleteRecord_InputMessage"/>
      <wsdl:output wsam:Action="http://www.totvs.com/IwsDataServer/DeleteRecordResponse" message="tns:IwsDataServer_DeleteRecord_OutputMessage"/>
    </wsdl:operation>
    <wsdl:operation name="DeleteRecordEmail">
      <wsdl:input wsam:Action="http://www.totvs.com/IwsDataServer/DeleteRecordEmail" message="tns:IwsDataServer_DeleteRecordEmail_InputMessage"/>
      <wsdl:output wsam:Action="http://www.totvs.com/IwsDataServer/DeleteRecordEmailResponse" message="tns:IwsDataServer_DeleteRecordEmail_OutputMessage"/>
    </wsdl:operation>
    <wsdl:operation name="DeleteRecordByKey">
      <wsdl:input wsam:Action="http://www.totvs.com/IwsDataServer/DeleteRecordByKey" message="tns:IwsDataServer_DeleteRecordByKey_InputMessage"/>
      <wsdl:output wsam:Action="http://www.totvs.com/IwsDataServer/DeleteRecordByKeyResponse" message="tns:IwsDataServer_DeleteRecordByKey_OutputMessage"/>
    </wsdl:operation>
    <wsdl:operation name="ReadLookupView">
      <wsdl:input wsam:Action="http://www.totvs.com/IwsDataServer/ReadLookupView" message="tns:IwsDataServer_ReadLookupView_InputMessage"/>
      <wsdl:output wsam:Action="http://www.totvs.com/IwsDataServer/ReadLookupViewResponse" message="tns:IwsDataServer_ReadLookupView_OutputMessage"/>
    </wsdl:operation>
    <wsdl:operation name="ReadLookupViewEmail">
      <wsdl:input wsam:Action="http://www.totvs.com/IwsDataServer/ReadLookupViewEmail" message="tns:IwsDataServer_ReadLookupViewEmail_InputMessage"/>
      <wsdl:output wsam:Action="http://www.totvs.com/IwsDataServer/ReadLookupViewEmailResponse" message="tns:IwsDataServer_ReadLookupViewEmail_OutputMessage"/>
    </wsdl:operation>
  </wsdl:portType>
  <wsdl:binding name="RM_IRMSServer" type="tns:IRMSServer">
    <soap:binding transport="http://schemas.xmlsoap.org/soap/http"/>
    <wsdl:operation name="Implements">
      <soap:operation soapAction="http://www.totvs.com/IRMSServer/Implements" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="CheckServiceActivity">
      <soap:operation soapAction="http://www.totvs.com/IRMSServer/CheckServiceActivity" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
  </wsdl:binding>
  <wsdl:binding name="RM_IwsBase" type="tns:IwsBase">
    <soap:binding transport="http://schemas.xmlsoap.org/soap/http"/>
    <wsdl:operation name="Implements">
      <soap:operation soapAction="http://www.totvs.com/IRMSServer/Implements" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="CheckServiceActivity">
      <soap:operation soapAction="http://www.totvs.com/IRMSServer/CheckServiceActivity" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="AutenticaAcesso">
      <soap:operation soapAction="http://www.totvs.com/IwsBase/AutenticaAcesso" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
  </wsdl:binding>
  <wsdl:binding name="RM_IwsDataServer" type="tns:IwsDataServer">
    <soap:binding transport="http://schemas.xmlsoap.org/soap/http"/>
    <wsdl:operation name="Implements">
      <soap:operation soapAction="http://www.totvs.com/IRMSServer/Implements" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="CheckServiceActivity">
      <soap:operation soapAction="http://www.totvs.com/IRMSServer/CheckServiceActivity" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetSchema">
      <soap:operation soapAction="http://www.totvs.com/IwsDataServer/GetSchema" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="IsValidDataServer">
      <soap:operation soapAction="http://www.totvs.com/IwsDataServer/IsValidDataServer" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetSchemaEmail">
      <soap:operation soapAction="http://www.totvs.com/IwsDataServer/GetSchemaEmail" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="ReadView">
      <soap:operation soapAction="http://www.totvs.com/IwsDataServer/ReadView" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="ReadViewEmail">
      <soap:operation soapAction="http://www.totvs.com/IwsDataServer/ReadViewEmail" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="ReadRecord">
      <soap:operation soapAction="http://www.totvs.com/IwsDataServer/ReadRecord" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="ReadRecordEmail">
      <soap:operation soapAction="http://www.totvs.com/IwsDataServer/ReadRecordEmail" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="SaveRecord">
      <soap:operation soapAction="http://www.totvs.com/IwsDataServer/SaveRecord" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="SaveRecordEmail">
      <soap:operation soapAction="http://www.totvs.com/IwsDataServer/SaveRecordEmail" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="DeleteRecord">
      <soap:operation soapAction="http://www.totvs.com/IwsDataServer/DeleteRecord" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="DeleteRecordEmail">
      <soap:operation soapAction="http://www.totvs.com/IwsDataServer/DeleteRecordEmail" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="DeleteRecordByKey">
      <soap:operation soapAction="http://www.totvs.com/IwsDataServer/DeleteRecordByKey" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="ReadLookupView">
      <soap:operation soapAction="http://www.totvs.com/IwsDataServer/ReadLookupView" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="ReadLookupViewEmail">
      <soap:operation soapAction="http://www.totvs.com/IwsDataServer/ReadLookupViewEmail" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
  </wsdl:binding>
  <wsdl:service name="wsDataServer">
    <wsdl:port name="RM_IRMSServer" binding="tns:RM_IRMSServer">
      <soap:address location="http://*************:8059/wsDataServer/IRMSServer"/>
    </wsdl:port>
    <wsdl:port name="RM_IwsBase" binding="tns:RM_IwsBase">
      <soap:address location="http://*************:8059/wsDataServer/IwsBase"/>
    </wsdl:port>
    <wsdl:port name="RM_IwsDataServer" binding="tns:RM_IwsDataServer">
      <soap:address location="http://*************:8059/wsDataServer/IwsDataServer"/>
    </wsdl:port>
  </wsdl:service>
</wsdl:definitions>]]></con:content><con:type>http://schemas.xmlsoap.org/wsdl/</con:type></con:part><con:part><con:url>http://*************:8059/wsDataServer/mex?xsd=xsd0</con:url><con:content><![CDATA[<xs:schema elementFormDefault="qualified" targetNamespace="http://www.totvs.com/" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:tns="http://www.totvs.com/">
  <xs:import schemaLocation="http://*************:8059/wsDataServer/mex?xsd=xsd2" namespace="http://schemas.datacontract.org/2004/07/System"/>
  <xs:element name="Implements">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="type" nillable="true" type="q1:Type" xmlns:q1="http://schemas.datacontract.org/2004/07/System"/>
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="ImplementsResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="ImplementsResult" type="xs:boolean"/>
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="CheckServiceActivity">
    <xs:complexType>
      <xs:sequence/>
    </xs:complexType>
  </xs:element>
  <xs:element name="CheckServiceActivityResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="CheckServiceActivityResult" type="xs:boolean"/>
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="AutenticaAcesso">
    <xs:complexType>
      <xs:sequence/>
    </xs:complexType>
  </xs:element>
  <xs:element name="AutenticaAcessoResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="AutenticaAcessoResult" nillable="true" type="xs:string"/>
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GetSchema">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="DataServerName" nillable="true" type="xs:string"/>
        <xs:element minOccurs="0" name="Contexto" nillable="true" type="xs:string"/>
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GetSchemaResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="GetSchemaResult" nillable="true" type="xs:string"/>
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="IsValidDataServer">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="DataServerName" nillable="true" type="xs:string"/>
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="IsValidDataServerResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="IsValidDataServerResult" nillable="true" type="xs:anyType"/>
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GetSchemaEmail">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="DataServerName" nillable="true" type="xs:string"/>
        <xs:element minOccurs="0" name="Contexto" nillable="true" type="xs:string"/>
        <xs:element minOccurs="0" name="EmailUsuarioContexto" nillable="true" type="xs:string"/>
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GetSchemaEmailResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="GetSchemaEmailResult" nillable="true" type="xs:string"/>
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="ReadView">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="DataServerName" nillable="true" type="xs:string"/>
        <xs:element minOccurs="0" name="Filtro" nillable="true" type="xs:string"/>
        <xs:element minOccurs="0" name="Contexto" nillable="true" type="xs:string"/>
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="ReadViewResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="ReadViewResult" nillable="true" type="xs:string"/>
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="ReadViewEmail">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="DataServerName" nillable="true" type="xs:string"/>
        <xs:element minOccurs="0" name="Filtro" nillable="true" type="xs:string"/>
        <xs:element minOccurs="0" name="Contexto" nillable="true" type="xs:string"/>
        <xs:element minOccurs="0" name="EmailUsuarioContexto" nillable="true" type="xs:string"/>
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="ReadViewEmailResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="ReadViewEmailResult" nillable="true" type="xs:string"/>
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="ReadRecord">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="DataServerName" nillable="true" type="xs:string"/>
        <xs:element minOccurs="0" name="PrimaryKey" nillable="true" type="xs:string"/>
        <xs:element minOccurs="0" name="Contexto" nillable="true" type="xs:string"/>
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="ReadRecordResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="ReadRecordResult" nillable="true" type="xs:string"/>
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="ReadRecordEmail">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="DataServerName" nillable="true" type="xs:string"/>
        <xs:element minOccurs="0" name="PrimaryKey" nillable="true" type="xs:string"/>
        <xs:element minOccurs="0" name="Contexto" nillable="true" type="xs:string"/>
        <xs:element minOccurs="0" name="EmailUsuarioContexto" nillable="true" type="xs:string"/>
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="ReadRecordEmailResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="ReadRecordEmailResult" nillable="true" type="xs:string"/>
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="SaveRecord">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="DataServerName" nillable="true" type="xs:string"/>
        <xs:element minOccurs="0" name="XML" nillable="true" type="xs:string"/>
        <xs:element minOccurs="0" name="Contexto" nillable="true" type="xs:string"/>
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="SaveRecordResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="SaveRecordResult" nillable="true" type="xs:string"/>
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="SaveRecordEmail">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="DataServerName" nillable="true" type="xs:string"/>
        <xs:element minOccurs="0" name="XML" nillable="true" type="xs:string"/>
        <xs:element minOccurs="0" name="Contexto" nillable="true" type="xs:string"/>
        <xs:element minOccurs="0" name="EmailUsuarioContexto" nillable="true" type="xs:string"/>
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="SaveRecordEmailResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="SaveRecordEmailResult" nillable="true" type="xs:string"/>
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="DeleteRecord">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="DataServerName" nillable="true" type="xs:string"/>
        <xs:element minOccurs="0" name="XML" nillable="true" type="xs:string"/>
        <xs:element minOccurs="0" name="Contexto" nillable="true" type="xs:string"/>
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="DeleteRecordResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="DeleteRecordResult" nillable="true" type="xs:string"/>
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="DeleteRecordEmail">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="DataServerName" nillable="true" type="xs:string"/>
        <xs:element minOccurs="0" name="XML" nillable="true" type="xs:string"/>
        <xs:element minOccurs="0" name="Contexto" nillable="true" type="xs:string"/>
        <xs:element minOccurs="0" name="EmailUsuarioContexto" nillable="true" type="xs:string"/>
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="DeleteRecordEmailResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="DeleteRecordEmailResult" nillable="true" type="xs:string"/>
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="DeleteRecordByKey">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="DataServerName" nillable="true" type="xs:string"/>
        <xs:element minOccurs="0" name="PrimaryKey" nillable="true" type="xs:string"/>
        <xs:element minOccurs="0" name="Contexto" nillable="true" type="xs:string"/>
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="DeleteRecordByKeyResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="DeleteRecordByKeyResult" nillable="true" type="xs:string"/>
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="ReadLookupView">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="DataServerName" nillable="true" type="xs:string"/>
        <xs:element minOccurs="0" name="Filtro" nillable="true" type="xs:string"/>
        <xs:element minOccurs="0" name="Contexto" nillable="true" type="xs:string"/>
        <xs:element minOccurs="0" name="OwnerData" nillable="true" type="xs:string"/>
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="ReadLookupViewResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="ReadLookupViewResult" nillable="true" type="xs:string"/>
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="ReadLookupViewEmail">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="DataServerName" nillable="true" type="xs:string"/>
        <xs:element minOccurs="0" name="Filtro" nillable="true" type="xs:string"/>
        <xs:element minOccurs="0" name="Contexto" nillable="true" type="xs:string"/>
        <xs:element minOccurs="0" name="OwnerData" nillable="true" type="xs:string"/>
        <xs:element minOccurs="0" name="EmailUsuarioContexto" nillable="true" type="xs:string"/>
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="ReadLookupViewEmailResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="ReadLookupViewEmailResult" nillable="true" type="xs:string"/>
      </xs:sequence>
    </xs:complexType>
  </xs:element>
</xs:schema>]]></con:content><con:type>http://www.w3.org/2001/XMLSchema</con:type></con:part><con:part><con:url>http://*************:8059/wsDataServer/mex?xsd=xsd2</con:url><con:content><![CDATA[<xs:schema elementFormDefault="qualified" targetNamespace="http://schemas.datacontract.org/2004/07/System" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:tns="http://schemas.datacontract.org/2004/07/System">
  <xs:import schemaLocation="http://*************:8059/wsDataServer/mex?xsd=xsd3" namespace="http://schemas.datacontract.org/2004/07/System.Reflection"/>
  <xs:complexType name="Type">
    <xs:complexContent mixed="false">
      <xs:extension base="q1:MemberInfo" xmlns:q1="http://schemas.datacontract.org/2004/07/System.Reflection">
        <xs:sequence/>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="Type" nillable="true" type="tns:Type"/>
</xs:schema>]]></con:content><con:type>http://www.w3.org/2001/XMLSchema</con:type></con:part><con:part><con:url>http://*************:8059/wsDataServer/mex?xsd=xsd3</con:url><con:content><![CDATA[<xs:schema elementFormDefault="qualified" targetNamespace="http://schemas.datacontract.org/2004/07/System.Reflection" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:tns="http://schemas.datacontract.org/2004/07/System.Reflection">
  <xs:complexType name="MemberInfo">
    <xs:sequence/>
  </xs:complexType>
  <xs:element name="MemberInfo" nillable="true" type="tns:MemberInfo"/>
</xs:schema>]]></con:content><con:type>http://www.w3.org/2001/XMLSchema</con:type></con:part><con:part><con:url>http://*************:8059/wsDataServer/mex?xsd=xsd1</con:url><con:content><![CDATA[<xs:schema attributeFormDefault="qualified" elementFormDefault="qualified" targetNamespace="http://schemas.microsoft.com/2003/10/Serialization/" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:tns="http://schemas.microsoft.com/2003/10/Serialization/">
  <xs:element name="anyType" nillable="true" type="xs:anyType"/>
  <xs:element name="anyURI" nillable="true" type="xs:anyURI"/>
  <xs:element name="base64Binary" nillable="true" type="xs:base64Binary"/>
  <xs:element name="boolean" nillable="true" type="xs:boolean"/>
  <xs:element name="byte" nillable="true" type="xs:byte"/>
  <xs:element name="dateTime" nillable="true" type="xs:dateTime"/>
  <xs:element name="decimal" nillable="true" type="xs:decimal"/>
  <xs:element name="double" nillable="true" type="xs:double"/>
  <xs:element name="float" nillable="true" type="xs:float"/>
  <xs:element name="int" nillable="true" type="xs:int"/>
  <xs:element name="long" nillable="true" type="xs:long"/>
  <xs:element name="QName" nillable="true" type="xs:QName"/>
  <xs:element name="short" nillable="true" type="xs:short"/>
  <xs:element name="string" nillable="true" type="xs:string"/>
  <xs:element name="unsignedByte" nillable="true" type="xs:unsignedByte"/>
  <xs:element name="unsignedInt" nillable="true" type="xs:unsignedInt"/>
  <xs:element name="unsignedLong" nillable="true" type="xs:unsignedLong"/>
  <xs:element name="unsignedShort" nillable="true" type="xs:unsignedShort"/>
  <xs:element name="char" nillable="true" type="tns:char"/>
  <xs:simpleType name="char">
    <xs:restriction base="xs:int"/>
  </xs:simpleType>
  <xs:element name="duration" nillable="true" type="tns:duration"/>
  <xs:simpleType name="duration">
    <xs:restriction base="xs:duration">
      <xs:pattern value="\-?P(\d*D)?(T(\d*H)?(\d*M)?(\d*(\.\d*)?S)?)?"/>
      <xs:minInclusive value="-P10675199DT2H48M5.4775808S"/>
      <xs:maxInclusive value="P10675199DT2H48M5.4775807S"/>
    </xs:restriction>
  </xs:simpleType>
  <xs:element name="guid" nillable="true" type="tns:guid"/>
  <xs:simpleType name="guid">
    <xs:restriction base="xs:string">
      <xs:pattern value="[\da-fA-F]{8}-[\da-fA-F]{4}-[\da-fA-F]{4}-[\da-fA-F]{4}-[\da-fA-F]{12}"/>
    </xs:restriction>
  </xs:simpleType>
  <xs:attribute name="FactoryType" type="xs:QName"/>
  <xs:attribute name="Id" type="xs:ID"/>
  <xs:attribute name="Ref" type="xs:IDREF"/>
</xs:schema>]]></con:content><con:type>http://www.w3.org/2001/XMLSchema</con:type></con:part></con:definitionCache><con:endpoints><con:endpoint>http://*************:8059/wsDataServer/IRMSServer</con:endpoint></con:endpoints><con:operation id="ca573bf0-8b32-4905-9103-2c8c9382e183" isOneWay="false" action="http://www.totvs.com/IRMSServer/CheckServiceActivity" name="CheckServiceActivity" bindingOperationName="CheckServiceActivity" type="Request-Response" inputName="" receivesAttachments="false" sendsAttachments="false" anonymous="optional"><con:settings/><con:call id="de1ee10f-33d5-4f11-8f04-4084c79f9511" name="Request 1"><con:settings/><con:encoding>UTF-8</con:encoding><con:endpoint>http://*************:8059/wsDataServer/IRMSServer</con:endpoint><con:request><![CDATA[<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:tot="http://www.totvs.com/">\r
   <soapenv:Header/>\r
   <soapenv:Body>\r
      <tot:CheckServiceActivity/>\r
   </soapenv:Body>\r
</soapenv:Envelope>]]></con:request><con:wsaConfig mustUnderstand="NONE" version="200508" action="http://www.totvs.com/IRMSServer/CheckServiceActivity"/></con:call></con:operation><con:operation id="8e17e49b-e401-4db1-a7ff-0947adfd26b0" isOneWay="false" action="http://www.totvs.com/IRMSServer/Implements" name="Implements" bindingOperationName="Implements" type="Request-Response" inputName="" receivesAttachments="false" sendsAttachments="false" anonymous="optional"><con:settings/><con:call id="12b6b994-6534-441c-bbd4-e56b90c51167" name="Request 1"><con:settings/><con:encoding>UTF-8</con:encoding><con:endpoint>http://*************:8059/wsDataServer/IRMSServer</con:endpoint><con:request><![CDATA[<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:tot="http://www.totvs.com/">\r
   <soapenv:Header/>\r
   <soapenv:Body>\r
      <tot:Implements>\r
         <!--Optional:-->\r
         <tot:type/>\r
      </tot:Implements>\r
   </soapenv:Body>\r
</soapenv:Envelope>]]></con:request><con:wsaConfig mustUnderstand="NONE" version="200508" action="http://www.totvs.com/IRMSServer/Implements"/></con:call></con:operation></con:interface><con:mockService id="68048b9b-ae48-4367-9c73-7881793cec4c" port="8080" path="/" host="DESKTOP-UJJIT8H" name="MockService 1"><con:settings><con:setting id="com.eviware.soapui.impl.wsdl.mock.WsdlMockService@require-soap-action">false</con:setting></con:settings><con:properties/></con:mockService><con:properties/><con:wssContainer/><con:oAuth2ProfileContainer/><con:oAuth1ProfileContainer/><con:sensitiveInformation/></con:soapui-project>