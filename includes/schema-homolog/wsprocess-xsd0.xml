<?xml version="1.0" encoding="utf-8"?><xs:schema elementFormDefault="qualified" targetNamespace="http://www.totvs.com/" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:tns="http://www.totvs.com/"><xs:import schemaLocation="wsprocess-xsd2.xml" namespace="http://schemas.datacontract.org/2004/07/System"/><xs:element name="Implements"><xs:complexType><xs:sequence><xs:element minOccurs="0" name="type" nillable="true" type="q1:Type" xmlns:q1="http://schemas.datacontract.org/2004/07/System"/></xs:sequence></xs:complexType></xs:element><xs:element name="ImplementsResponse"><xs:complexType><xs:sequence><xs:element minOccurs="0" name="ImplementsResult" type="xs:boolean"/></xs:sequence></xs:complexType></xs:element><xs:element name="CheckServiceActivity"><xs:complexType><xs:sequence/></xs:complexType></xs:element><xs:element name="CheckServiceActivityResponse"><xs:complexType><xs:sequence><xs:element minOccurs="0" name="CheckServiceActivityResult" type="xs:boolean"/></xs:sequence></xs:complexType></xs:element><xs:element name="AutenticaAcesso"><xs:complexType><xs:sequence/></xs:complexType></xs:element><xs:element name="AutenticaAcessoResponse"><xs:complexType><xs:sequence><xs:element minOccurs="0" name="AutenticaAcessoResult" nillable="true" type="xs:string"/></xs:sequence></xs:complexType></xs:element><xs:element name="GetSchema"><xs:complexType><xs:sequence><xs:element minOccurs="0" name="ProcessServerName" nillable="true" type="xs:string"/></xs:sequence></xs:complexType></xs:element><xs:element name="GetSchemaResponse"><xs:complexType><xs:sequence><xs:element minOccurs="0" name="GetSchemaResult" nillable="true" type="xs:string"/></xs:sequence></xs:complexType></xs:element><xs:element name="GetSchema2"><xs:complexType><xs:sequence><xs:element minOccurs="0" name="ProcessServerName" nillable="true" type="xs:string"/><xs:element minOccurs="0" name="ownerData" nillable="true" type="xs:string"/></xs:sequence></xs:complexType></xs:element><xs:element name="GetSchema2Response"><xs:complexType><xs:sequence><xs:element minOccurs="0" name="GetSchema2Result" nillable="true" type="xs:string"/></xs:sequence></xs:complexType></xs:element><xs:element name="ExecuteProcess"><xs:complexType><xs:sequence><xs:element minOccurs="0" name="ProcessServerName" nillable="true" type="xs:string"/></xs:sequence></xs:complexType></xs:element><xs:element name="ExecuteProcessResponse"><xs:complexType><xs:sequence><xs:element minOccurs="0" name="ExecuteProcessResult" nillable="true" type="xs:string"/></xs:sequence></xs:complexType></xs:element><xs:element name="ExecuteWithParams"><xs:complexType><xs:sequence><xs:element minOccurs="0" name="ProcessServerName" nillable="true" type="xs:string"/><xs:element minOccurs="0" name="strXmlParams" nillable="true" type="xs:string"/></xs:sequence></xs:complexType></xs:element><xs:element name="ExecuteWithParamsResponse"><xs:complexType><xs:sequence><xs:element minOccurs="0" name="ExecuteWithParamsResult" nillable="true" type="xs:string"/></xs:sequence></xs:complexType></xs:element><xs:element name="ExecuteWithParamsAsync"><xs:complexType><xs:sequence><xs:element minOccurs="0" name="ProcessServerName" nillable="true" type="xs:string"/><xs:element minOccurs="0" name="strXmlParams" nillable="true" type="xs:string"/></xs:sequence></xs:complexType></xs:element><xs:element name="ExecuteWithParamsAsyncResponse"><xs:complexType><xs:sequence><xs:element minOccurs="0" name="ExecuteWithParamsAsyncResult" nillable="true" type="xs:string"/></xs:sequence></xs:complexType></xs:element><xs:element name="ExecuteWithXmlParams"><xs:complexType><xs:sequence><xs:element minOccurs="0" name="ProcessServerName" nillable="true" type="xs:string"/><xs:element minOccurs="0" name="strXmlParams" nillable="true" type="xs:string"/></xs:sequence></xs:complexType></xs:element><xs:element name="ExecuteWithXmlParamsResponse"><xs:complexType><xs:sequence><xs:element minOccurs="0" name="ExecuteWithXmlParamsResult" nillable="true" type="xs:string"/></xs:sequence></xs:complexType></xs:element><xs:element name="ExecuteWithXmlParamsAsync"><xs:complexType><xs:sequence><xs:element minOccurs="0" name="ProcessServerName" nillable="true" type="xs:string"/><xs:element minOccurs="0" name="strXmlParams" nillable="true" type="xs:string"/></xs:sequence></xs:complexType></xs:element><xs:element name="ExecuteWithXmlParamsAsyncResponse"><xs:complexType><xs:sequence><xs:element minOccurs="0" name="ExecuteWithXmlParamsAsyncResult" nillable="true" type="xs:string"/></xs:sequence></xs:complexType></xs:element><xs:element name="GetProcessStatus"><xs:complexType><xs:sequence><xs:element minOccurs="0" name="jobId" type="xs:int"/><xs:element minOccurs="0" name="execId" type="xs:int"/></xs:sequence></xs:complexType></xs:element><xs:element name="GetProcessStatusResponse"><xs:complexType><xs:sequence><xs:element minOccurs="0" name="GetProcessStatusResult" nillable="true" type="xs:string"/></xs:sequence></xs:complexType></xs:element></xs:schema>