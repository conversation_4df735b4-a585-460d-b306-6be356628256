<?php 

class Utils{

	public function acessoElibro($email){
		// Configuração do cURL
		$url = "https://elibrosso.trevisan.edu.br/";
		$data = array("email" => $email); 

		// Inicia a sessão cURL
		$ch = curl_init($url);

		// Configura as opções do cURL
		curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
		curl_setopt($ch, CURLOPT_POST, true);
		curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
		curl_setopt($ch, CURLOPT_HTTPHEADER, array('Content-Type:application/json'));

		// Executa a requisição cURL e captura a resposta
		$response = curl_exec($ch);

		// Verifica se houve erro na requisição
		if(curl_errno($ch)) {
		    echo 'Erro na requisição: ' . curl_error($ch);
		    exit;
		}

		// Fecha a sessão cURL
		curl_close($ch);

		// Converte a resposta JSON para objeto
		$responseObj = json_decode($response);

		// Captura a URL do objeto de resposta
		if(isset($responseObj->url)) {
		    $redirectUrl = $responseObj->url;
		    // Redireciona para a URL capturada
		    header("Location: $redirectUrl");
		    exit;
		} else {
		    echo 'Erro: URL não encontrada na resposta';
		}

	}

}

?>