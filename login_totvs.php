<?php
// --- CONFIGURAÇÃO ---
// ATENÇÃO: Substitua 'https://servidor.totvs.com.br' pela URL base do seu portal TOTVS.
// Exemplo: 'https://faculdadetrevisan150171.rm.cloudtotvs.com.br:2106'
$totvs_server_url = 'https://homologa.estudante.trevisan.edu.br:2104'; 
$totvs_login_path = '/Corpore.Net/Source/EDU-EDUCACIONAL/Public/EduPortalAlunoLogin.aspx?AutoLoginType=ExternalLogin';

$totvs_alias = 'CorporeRM-dev';
// --- FIM DA CONFIGURAÇÃO ---
session_start();

$username = isset($_SESSION['usuario']) ? $_SESSION['usuario'] : 'Usuário não definido';
$password = isset($_SESSION['senha']) ? $_SESSION['senha'] : 'Senha não definida';

echo "Usuário: " . htmlspecialchars($usuario) . "<br>";
echo "Senha: " . htmlspecialchars($senha);

 
// Verifica se o formulário foi enviado (se a requisição é um POST)
if (!empty($username) && !empty($password)) {
    
 

    // Monta a URL de destino completa
    $action_url = htmlspecialchars($totvs_server_url . $totvs_login_path, ENT_QUOTES, 'UTF-8');

    // Gera uma página HTML com um formulário oculto que será submetido automaticamente via JavaScript.
    // Isso simula o envio dos dados diretamente do navegador do usuário para o portal TOTVS.
    echo <<<HTML
<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Redirecionando para o Portal...</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;700&display=swap');
        body { font-family: 'Inter', sans-serif; }
    </style>
</head>
<body class="bg-gray-100 flex items-center justify-center h-screen">
    <div class="text-center">
        <h1 class="text-2xl font-bold text-gray-700">Aguarde, por favor...</h1>
        <p class="text-gray-500 mt-2">Estamos realizando seu login no portal TOTVS.</p>
        
        <!-- Formulário oculto que será enviado para o TOTVS -->
        <form name="login_totvs" id="login_totvs" method="post" action="{$action_url}">
            <input type="hidden" name="user" value="{$username}" />
            <input type="hidden" name="pass" value="{$password}" />
            <input type="hidden" name="alias" value="{$totvs_alias}" />
        </form>

        <script type="text/javascript">
            // Submete o formulário assim que a página for carregada.
            window.onload = function() {
                document.getElementById('login_totvs').submit();
            };
        </script>
    </div>
</body>
</html>
HTML;
    // Encerra o script após gerar a página de redirecionamento.
    exit;

}  
?>
