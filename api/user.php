<?php 
if ($_SERVER['REQUEST_METHOD'] === 'GET' && strpos($_SERVER['REQUEST_URI'], '/api/user') !== false) {
    $authHeader = $_SERVER['HTTP_AUTHORIZATION'] ?? '';
    if (!preg_match('/Bearer\s(\S+)/', $authHeader, $matches)) {
        http_response_code(401);
        echo json_encode(['error' => 'unauthorized']);
        exit;
    }

    $token = $matches[1];
    $file = "/tmp/oauth_tokens/{$token}";
    if (!file_exists($file)) {
        http_response_code(401);
        echo json_encode(['error' => 'invalid_token']);
        exit;
    }

    $data = json_decode(file_get_contents($file), true);
    $username = $data['username'];

    header('Content-Type: application/json');
    echo json_encode([
        'sub' => $username,
        'email' => $username . '@example.com',
        'name' => $username,
        'given_name' => $username,
        'family_name' => 'Trevisan'
    ]);
    exit;
}
