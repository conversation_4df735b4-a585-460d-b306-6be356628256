<?php
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);
 
$db = new SQLite3('acessos.db');

// Busca todos os registros
$result = $db->query("SELECT * FROM acessos ORDER BY created_at DESC");

// Mostra os dados
echo "<h2>Registros de Acesso</h2><table border='1' cellpadding='5'>";
echo "<tr><th>ID</th><th>Usuário</th><th>IP</th><th>Tipo</th><th>Resposta</th><th>Status</th><th>Data</th></tr>";

while ($row = $result->fetchArray(SQLITE3_ASSOC)) {
    echo "<tr>
            <td>{$row['id']}</td>
            <td>{$row['username']}</td>
            <td>{$row['ip']}</td>
            <td>{$row['tipo']}</td>
            <td>{$row['response']}</td>
            <td>{$row['status']}</td>
            <td>{$row['created_at']}</td>
          </tr>";
}
echo "</table>";
?>
