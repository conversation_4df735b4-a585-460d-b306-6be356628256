# Implementações de Debug e Performance - Sistema de Middleware de Login

## Resumo das Modificações

Este documento descreve as implementações realizadas no sistema de middleware de login para adicionar debugs de tempo e otimizações de performance.

## 1. Sistema de Debug de Tempo

### Funções Implementadas

#### `writeDebugLog($message, $startTime = null, $operation = 'GERAL')`
- Escreve logs com timestamp preciso no arquivo `debug_login.log`
- Calcula tempo de execução quando `$startTime` é fornecido
- Formato: `[TIMESTAMP] [OPERACAO] MENSAGEM [TEMPO: X.XX ms]`

#### `startTimer($operation = 'OPERACAO')`
- Inicia medição de tempo para uma operação
- Retorna timestamp para uso com `endTimer()`
- Registra início da operação no log

#### `endTimer($startTime, $operation = 'OPERACAO', $additionalInfo = '')`
- Finaliza medição de tempo
- Calcula duração em milissegundos
- Registra fim da operação com tempo total

### Pontos de Debug Implementados

#### Classe WSTotvs
- **Login TOTVS**: Tempo total de autenticação
- **Criação SoapClient**: Tempo de criação de clientes SOAP
- **Chamadas SOAP**: Tempo de execução de métodos SOAP
- **Consultas SQL**: Tempo de execução via SOAP (buscaNomeBase, getEmployeeId)

#### Classe WSMoodle
- **Chamadas REST API**: Tempo de requisições ao Moodle
- **HTTP POST**: Tempo de requisições HTTP
- **JSON Decode**: Tempo de processamento JSON
- **Conduit Operations**: Tempo de operações via Conduit

#### Classe WSTotvsRest
- **Consultas SQL REST**: Tempo de consultas via API REST
- **Execução cURL**: Tempo de requisições HTTP

#### Função Principal
- **Autenticação Total**: Tempo completo do processo de login
- **Criação de Objetos**: Tempo de instanciação de classes
- **Redirecionamentos**: Tempo de preparação de URLs
- **Consultas Moodle**: Tempo de verificação de usuários

## 2. Otimizações de Performance

### Timeouts Otimizados

#### Classe Curl
- **CURLOPT_CONNECTTIMEOUT**: Reduzido de 30s para 15s
- **CURLOPT_TIMEOUT**: Adicionado timeout total de 45s
- **Keep-Alive**: Habilitado para reutilização de conexões
  - `CURLOPT_TCP_KEEPALIVE = 1`
  - `CURLOPT_TCP_KEEPIDLE = 30`
  - `CURLOPT_TCP_KEEPINTVL = 15`

#### Classe WSTotvsRest
- **CURLOPT_TIMEOUT**: Reduzido de 30s para 20s
- **CURLOPT_CONNECTTIMEOUT**: Adicionado timeout de 10s
- **Keep-Alive**: Habilitado para conexões HTTP
- **Headers**: Adicionado `Connection: keep-alive`

#### Classe WSTotvs
- **connection_timeout**: Adicionado timeout de 15s para SOAP
- **default_socket_timeout**: Adicionado timeout de 30s

### Cache e Reutilização

#### Cache de Clientes SOAP
- Implementado cache estático para clientes SOAP
- Método `getSoapClient()` para reutilização
- Reduz overhead de criação de conexões

#### Padrão Singleton
- Implementado singleton para classe WSMoodle
- Método `getInstance()` para reutilização de instâncias
- Reduz criação desnecessária de objetos

## 3. Arquivos Modificados

### `index.php`
- Adicionadas funções de debug no início do arquivo
- Debugs implementados em todas as operações críticas
- Otimizações de performance nas classes WSTotvs e WSMoodle

### `Curl.class.php`
- Otimizações de timeout e keep-alive
- Configurações mais eficientes para conexões HTTP

### `includes/WSTotvsRest.class.php`
- Debugs de tempo para consultas SQL REST
- Otimizações de timeout e keep-alive

## 4. Arquivo de Log

### Localização
- `debug_login.log` na raiz do projeto

### Formato dos Logs
```
[2025-07-22 16:37:31.000000] [OPERACAO] MENSAGEM [TEMPO: 100.12 ms]
```

### Tipos de Operações Logadas
- `TIMER`: Início e fim de operações
- `LOGIN_TOTVS`: Autenticação no sistema TOTVS
- `MOODLE_REST_API`: Chamadas à API REST do Moodle
- `SOAP_CALL`: Chamadas SOAP
- `HTTP_POST`: Requisições HTTP POST
- `AUTENTICACAO_TOTAL`: Processo completo de autenticação

## 5. Benefícios Implementados

### Debugging
- Visibilidade completa dos tempos de execução
- Identificação de gargalos de performance
- Logs detalhados para análise posterior
- Medições precisas em milissegundos

### Performance
- Redução de timeouts desnecessários
- Reutilização de conexões HTTP/SOAP
- Cache de objetos para evitar recriações
- Configurações otimizadas para ambiente de produção

## 6. Como Usar

### Monitoramento
1. Acesse o sistema normalmente
2. Verifique o arquivo `debug_login.log`
3. Analise os tempos de cada operação
4. Identifique gargalos baseado nos logs

### Análise de Performance
- Tempos > 1000ms: Possível gargalo de rede
- Tempos > 5000ms: Problema crítico de performance
- Compare tempos entre diferentes usuários/horários

## 7. Próximos Passos Recomendados

1. **Monitoramento em Produção**: Implementar rotação de logs
2. **Alertas**: Configurar alertas para tempos excessivos
3. **Métricas**: Implementar dashboard de performance
4. **Cache Avançado**: Considerar cache de resultados de consultas
